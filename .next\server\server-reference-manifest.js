self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"00a987e12983525aec8deb75118d3e56bbb2fcc77a\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Ckomal%5C%5CDocuments%5C%5Cjsm-apps%5C%5Cnext%5C%5Cecom%5C%5Cv0-download-attempt%5C%5Capp%5C%5Cadmin%5C%5Cproducts%5C%5Cactions.ts%22%2C%5B%7B%22id%22%3A%2200a987e12983525aec8deb75118d3e56bbb2fcc77a%22%2C%22exportedName%22%3A%22getProducts%22%7D%2C%7B%22id%22%3A%22403dc49674e28b1032ab35d2cded600eac7bff76b9%22%2C%22exportedName%22%3A%22deleteProduct%22%7D%2C%7B%22id%22%3A%2240baab106c2b0136024f83eb5890b1d52cd2d1acb2%22%2C%22exportedName%22%3A%22createProduct%22%7D%2C%7B%22id%22%3A%22604383b1be6460789a4f672e0939f23be236a1bcea%22%2C%22exportedName%22%3A%22updateProduct%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"403dc49674e28b1032ab35d2cded600eac7bff76b9\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Ckomal%5C%5CDocuments%5C%5Cjsm-apps%5C%5Cnext%5C%5Cecom%5C%5Cv0-download-attempt%5C%5Capp%5C%5Cadmin%5C%5Cproducts%5C%5Cactions.ts%22%2C%5B%7B%22id%22%3A%2200a987e12983525aec8deb75118d3e56bbb2fcc77a%22%2C%22exportedName%22%3A%22getProducts%22%7D%2C%7B%22id%22%3A%22403dc49674e28b1032ab35d2cded600eac7bff76b9%22%2C%22exportedName%22%3A%22deleteProduct%22%7D%2C%7B%22id%22%3A%2240baab106c2b0136024f83eb5890b1d52cd2d1acb2%22%2C%22exportedName%22%3A%22createProduct%22%7D%2C%7B%22id%22%3A%22604383b1be6460789a4f672e0939f23be236a1bcea%22%2C%22exportedName%22%3A%22updateProduct%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"40baab106c2b0136024f83eb5890b1d52cd2d1acb2\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Ckomal%5C%5CDocuments%5C%5Cjsm-apps%5C%5Cnext%5C%5Cecom%5C%5Cv0-download-attempt%5C%5Capp%5C%5Cadmin%5C%5Cproducts%5C%5Cactions.ts%22%2C%5B%7B%22id%22%3A%2200a987e12983525aec8deb75118d3e56bbb2fcc77a%22%2C%22exportedName%22%3A%22getProducts%22%7D%2C%7B%22id%22%3A%22403dc49674e28b1032ab35d2cded600eac7bff76b9%22%2C%22exportedName%22%3A%22deleteProduct%22%7D%2C%7B%22id%22%3A%2240baab106c2b0136024f83eb5890b1d52cd2d1acb2%22%2C%22exportedName%22%3A%22createProduct%22%7D%2C%7B%22id%22%3A%22604383b1be6460789a4f672e0939f23be236a1bcea%22%2C%22exportedName%22%3A%22updateProduct%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"604383b1be6460789a4f672e0939f23be236a1bcea\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Ckomal%5C%5CDocuments%5C%5Cjsm-apps%5C%5Cnext%5C%5Cecom%5C%5Cv0-download-attempt%5C%5Capp%5C%5Cadmin%5C%5Cproducts%5C%5Cactions.ts%22%2C%5B%7B%22id%22%3A%2200a987e12983525aec8deb75118d3e56bbb2fcc77a%22%2C%22exportedName%22%3A%22getProducts%22%7D%2C%7B%22id%22%3A%22403dc49674e28b1032ab35d2cded600eac7bff76b9%22%2C%22exportedName%22%3A%22deleteProduct%22%7D%2C%7B%22id%22%3A%2240baab106c2b0136024f83eb5890b1d52cd2d1acb2%22%2C%22exportedName%22%3A%22createProduct%22%7D%2C%7B%22id%22%3A%22604383b1be6460789a4f672e0939f23be236a1bcea%22%2C%22exportedName%22%3A%22updateProduct%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\"\n}"