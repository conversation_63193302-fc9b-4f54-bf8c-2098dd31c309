"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+postgrest-js@1.19.4";
exports.ids = ["vendor-chunks/@supabase+postgrest-js@1.19.4"];
exports.modules = {

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js ***!
  \***************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n// @ts-ignore\nconst node_fetch_1 = __importDefault(__webpack_require__(/*! @supabase/node-fetch */ \"(action-browser)/./node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\"));\nconst PostgrestError_1 = __importDefault(__webpack_require__(/*! ./PostgrestError */ \"(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js\"));\nclass PostgrestBuilder {\n    constructor(builder) {\n        this.shouldThrowOnError = false;\n        this.method = builder.method;\n        this.url = builder.url;\n        this.headers = builder.headers;\n        this.schema = builder.schema;\n        this.body = builder.body;\n        this.shouldThrowOnError = builder.shouldThrowOnError;\n        this.signal = builder.signal;\n        this.isMaybeSingle = builder.isMaybeSingle;\n        if (builder.fetch) {\n            this.fetch = builder.fetch;\n        }\n        else if (typeof fetch === 'undefined') {\n            this.fetch = node_fetch_1.default;\n        }\n        else {\n            this.fetch = fetch;\n        }\n    }\n    /**\n     * If there's an error with the query, throwOnError will reject the promise by\n     * throwing the error instead of returning it as part of a successful response.\n     *\n     * {@link https://github.com/supabase/supabase-js/issues/92}\n     */\n    throwOnError() {\n        this.shouldThrowOnError = true;\n        return this;\n    }\n    /**\n     * Set an HTTP header for the request.\n     */\n    setHeader(name, value) {\n        this.headers = Object.assign({}, this.headers);\n        this.headers[name] = value;\n        return this;\n    }\n    then(onfulfilled, onrejected) {\n        // https://postgrest.org/en/stable/api.html#switching-schemas\n        if (this.schema === undefined) {\n            // skip\n        }\n        else if (['GET', 'HEAD'].includes(this.method)) {\n            this.headers['Accept-Profile'] = this.schema;\n        }\n        else {\n            this.headers['Content-Profile'] = this.schema;\n        }\n        if (this.method !== 'GET' && this.method !== 'HEAD') {\n            this.headers['Content-Type'] = 'application/json';\n        }\n        // NOTE: Invoke w/o `this` to avoid illegal invocation error.\n        // https://github.com/supabase/postgrest-js/pull/247\n        const _fetch = this.fetch;\n        let res = _fetch(this.url.toString(), {\n            method: this.method,\n            headers: this.headers,\n            body: JSON.stringify(this.body),\n            signal: this.signal,\n        }).then(async (res) => {\n            var _a, _b, _c;\n            let error = null;\n            let data = null;\n            let count = null;\n            let status = res.status;\n            let statusText = res.statusText;\n            if (res.ok) {\n                if (this.method !== 'HEAD') {\n                    const body = await res.text();\n                    if (body === '') {\n                        // Prefer: return=minimal\n                    }\n                    else if (this.headers['Accept'] === 'text/csv') {\n                        data = body;\n                    }\n                    else if (this.headers['Accept'] &&\n                        this.headers['Accept'].includes('application/vnd.pgrst.plan+text')) {\n                        data = body;\n                    }\n                    else {\n                        data = JSON.parse(body);\n                    }\n                }\n                const countHeader = (_a = this.headers['Prefer']) === null || _a === void 0 ? void 0 : _a.match(/count=(exact|planned|estimated)/);\n                const contentRange = (_b = res.headers.get('content-range')) === null || _b === void 0 ? void 0 : _b.split('/');\n                if (countHeader && contentRange && contentRange.length > 1) {\n                    count = parseInt(contentRange[1]);\n                }\n                // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n                // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n                if (this.isMaybeSingle && this.method === 'GET' && Array.isArray(data)) {\n                    if (data.length > 1) {\n                        error = {\n                            // https://github.com/PostgREST/postgrest/blob/a867d79c42419af16c18c3fb019eba8df992626f/src/PostgREST/Error.hs#L553\n                            code: 'PGRST116',\n                            details: `Results contain ${data.length} rows, application/vnd.pgrst.object+json requires 1 row`,\n                            hint: null,\n                            message: 'JSON object requested, multiple (or no) rows returned',\n                        };\n                        data = null;\n                        count = null;\n                        status = 406;\n                        statusText = 'Not Acceptable';\n                    }\n                    else if (data.length === 1) {\n                        data = data[0];\n                    }\n                    else {\n                        data = null;\n                    }\n                }\n            }\n            else {\n                const body = await res.text();\n                try {\n                    error = JSON.parse(body);\n                    // Workaround for https://github.com/supabase/postgrest-js/issues/295\n                    if (Array.isArray(error) && res.status === 404) {\n                        data = [];\n                        error = null;\n                        status = 200;\n                        statusText = 'OK';\n                    }\n                }\n                catch (_d) {\n                    // Workaround for https://github.com/supabase/postgrest-js/issues/295\n                    if (res.status === 404 && body === '') {\n                        status = 204;\n                        statusText = 'No Content';\n                    }\n                    else {\n                        error = {\n                            message: body,\n                        };\n                    }\n                }\n                if (error && this.isMaybeSingle && ((_c = error === null || error === void 0 ? void 0 : error.details) === null || _c === void 0 ? void 0 : _c.includes('0 rows'))) {\n                    error = null;\n                    status = 200;\n                    statusText = 'OK';\n                }\n                if (error && this.shouldThrowOnError) {\n                    throw new PostgrestError_1.default(error);\n                }\n            }\n            const postgrestResponse = {\n                error,\n                data,\n                count,\n                status,\n                statusText,\n            };\n            return postgrestResponse;\n        });\n        if (!this.shouldThrowOnError) {\n            res = res.catch((fetchError) => {\n                var _a, _b, _c;\n                return ({\n                    error: {\n                        message: `${(_a = fetchError === null || fetchError === void 0 ? void 0 : fetchError.name) !== null && _a !== void 0 ? _a : 'FetchError'}: ${fetchError === null || fetchError === void 0 ? void 0 : fetchError.message}`,\n                        details: `${(_b = fetchError === null || fetchError === void 0 ? void 0 : fetchError.stack) !== null && _b !== void 0 ? _b : ''}`,\n                        hint: '',\n                        code: `${(_c = fetchError === null || fetchError === void 0 ? void 0 : fetchError.code) !== null && _c !== void 0 ? _c : ''}`,\n                    },\n                    data: null,\n                    count: null,\n                    status: 0,\n                    statusText: '',\n                });\n            });\n        }\n        return res.then(onfulfilled, onrejected);\n    }\n    /**\n     * Override the type of the returned `data`.\n     *\n     * @typeParam NewResult - The new result type to override with\n     * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n     */\n    returns() {\n        /* istanbul ignore next */\n        return this;\n    }\n    /**\n     * Override the type of the returned `data` field in the response.\n     *\n     * @typeParam NewResult - The new type to cast the response data to\n     * @typeParam Options - Optional type configuration (defaults to { merge: true })\n     * @typeParam Options.merge - When true, merges the new type with existing return type. When false, replaces the existing types entirely (defaults to true)\n     * @example\n     * ```typescript\n     * // Merge with existing types (default behavior)\n     * const query = supabase\n     *   .from('users')\n     *   .select()\n     *   .overrideTypes<{ custom_field: string }>()\n     *\n     * // Replace existing types completely\n     * const replaceQuery = supabase\n     *   .from('users')\n     *   .select()\n     *   .overrideTypes<{ id: number; name: string }, { merge: false }>()\n     * ```\n     * @returns A PostgrestBuilder instance with the new type\n     */\n    overrideTypes() {\n        return this;\n    }\n}\nexports[\"default\"] = PostgrestBuilder;\n//# sourceMappingURL=PostgrestBuilder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js ***!
  \**************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst PostgrestQueryBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestQueryBuilder */ \"(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js\"));\nconst PostgrestFilterBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestFilterBuilder */ \"(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\"));\nconst constants_1 = __webpack_require__(/*! ./constants */ \"(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/constants.js\");\n/**\n * PostgREST client.\n *\n * @typeParam Database - Types for the schema from the [type\n * generator](https://supabase.com/docs/reference/javascript/next/typescript-support)\n *\n * @typeParam SchemaName - Postgres schema to switch to. Must be a string\n * literal, the same one passed to the constructor. If the schema is not\n * `\"public\"`, this must be supplied manually.\n */\nclass PostgrestClient {\n    // TODO: Add back shouldThrowOnError once we figure out the typings\n    /**\n     * Creates a PostgREST client.\n     *\n     * @param url - URL of the PostgREST endpoint\n     * @param options - Named parameters\n     * @param options.headers - Custom headers\n     * @param options.schema - Postgres schema to switch to\n     * @param options.fetch - Custom fetch\n     */\n    constructor(url, { headers = {}, schema, fetch, } = {}) {\n        this.url = url;\n        this.headers = Object.assign(Object.assign({}, constants_1.DEFAULT_HEADERS), headers);\n        this.schemaName = schema;\n        this.fetch = fetch;\n    }\n    /**\n     * Perform a query on a table or a view.\n     *\n     * @param relation - The table or view name to query\n     */\n    from(relation) {\n        const url = new URL(`${this.url}/${relation}`);\n        return new PostgrestQueryBuilder_1.default(url, {\n            headers: Object.assign({}, this.headers),\n            schema: this.schemaName,\n            fetch: this.fetch,\n        });\n    }\n    /**\n     * Select a schema to query or perform an function (rpc) call.\n     *\n     * The schema needs to be on the list of exposed schemas inside Supabase.\n     *\n     * @param schema - The schema to query\n     */\n    schema(schema) {\n        return new PostgrestClient(this.url, {\n            headers: this.headers,\n            schema,\n            fetch: this.fetch,\n        });\n    }\n    /**\n     * Perform a function call.\n     *\n     * @param fn - The function name to call\n     * @param args - The arguments to pass to the function call\n     * @param options - Named parameters\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     * @param options.get - When set to `true`, the function will be called with\n     * read-only access mode.\n     * @param options.count - Count algorithm to use to count rows returned by the\n     * function. Only applicable for [set-returning\n     * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    rpc(fn, args = {}, { head = false, get = false, count, } = {}) {\n        let method;\n        const url = new URL(`${this.url}/rpc/${fn}`);\n        let body;\n        if (head || get) {\n            method = head ? 'HEAD' : 'GET';\n            Object.entries(args)\n                // params with undefined value needs to be filtered out, otherwise it'll\n                // show up as `?param=undefined`\n                .filter(([_, value]) => value !== undefined)\n                // array values need special syntax\n                .map(([name, value]) => [name, Array.isArray(value) ? `{${value.join(',')}}` : `${value}`])\n                .forEach(([name, value]) => {\n                url.searchParams.append(name, value);\n            });\n        }\n        else {\n            method = 'POST';\n            body = args;\n        }\n        const headers = Object.assign({}, this.headers);\n        if (count) {\n            headers['Prefer'] = `count=${count}`;\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url,\n            headers,\n            schema: this.schemaName,\n            body,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n}\nexports[\"default\"] = PostgrestClient;\n//# sourceMappingURL=PostgrestClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/**\n * Error format\n *\n * {@link https://postgrest.org/en/stable/api.html?highlight=options#errors-and-http-status-codes}\n */\nclass PostgrestError extends Error {\n    constructor(context) {\n        super(context.message);\n        this.name = 'PostgrestError';\n        this.details = context.details;\n        this.hint = context.hint;\n        this.code = context.code;\n    }\n}\nexports[\"default\"] = PostgrestError;\n//# sourceMappingURL=PostgrestError.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9Ac3VwYWJhc2UrcG9zdGdyZXN0LWpzQDEuMTkuNC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Bvc3RncmVzdC1qcy9kaXN0L2Nqcy9Qb3N0Z3Jlc3RFcnJvci5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RDtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBZTtBQUNmIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGtvbWFsXFxEb2N1bWVudHNcXGpzbS1hcHBzXFxuZXh0XFxlY29tXFx2MC1kb3dubG9hZC1hdHRlbXB0XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2UrcG9zdGdyZXN0LWpzQDEuMTkuNFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHBvc3RncmVzdC1qc1xcZGlzdFxcY2pzXFxQb3N0Z3Jlc3RFcnJvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8qKlxuICogRXJyb3IgZm9ybWF0XG4gKlxuICoge0BsaW5rIGh0dHBzOi8vcG9zdGdyZXN0Lm9yZy9lbi9zdGFibGUvYXBpLmh0bWw/aGlnaGxpZ2h0PW9wdGlvbnMjZXJyb3JzLWFuZC1odHRwLXN0YXR1cy1jb2Rlc31cbiAqL1xuY2xhc3MgUG9zdGdyZXN0RXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gICAgY29uc3RydWN0b3IoY29udGV4dCkge1xuICAgICAgICBzdXBlcihjb250ZXh0Lm1lc3NhZ2UpO1xuICAgICAgICB0aGlzLm5hbWUgPSAnUG9zdGdyZXN0RXJyb3InO1xuICAgICAgICB0aGlzLmRldGFpbHMgPSBjb250ZXh0LmRldGFpbHM7XG4gICAgICAgIHRoaXMuaGludCA9IGNvbnRleHQuaGludDtcbiAgICAgICAgdGhpcy5jb2RlID0gY29udGV4dC5jb2RlO1xuICAgIH1cbn1cbmV4cG9ydHMuZGVmYXVsdCA9IFBvc3RncmVzdEVycm9yO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9UG9zdGdyZXN0RXJyb3IuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js ***!
  \*********************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst PostgrestTransformBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestTransformBuilder */ \"(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js\"));\nclass PostgrestFilterBuilder extends PostgrestTransformBuilder_1.default {\n    /**\n     * Match only rows where `column` is equal to `value`.\n     *\n     * To check if the value of `column` is NULL, you should use `.is()` instead.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    eq(column, value) {\n        this.url.searchParams.append(column, `eq.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is not equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    neq(column, value) {\n        this.url.searchParams.append(column, `neq.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is greater than `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    gt(column, value) {\n        this.url.searchParams.append(column, `gt.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is greater than or equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    gte(column, value) {\n        this.url.searchParams.append(column, `gte.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is less than `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    lt(column, value) {\n        this.url.searchParams.append(column, `lt.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is less than or equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    lte(column, value) {\n        this.url.searchParams.append(column, `lte.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches `pattern` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param pattern - The pattern to match with\n     */\n    like(column, pattern) {\n        this.url.searchParams.append(column, `like.${pattern}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches all of `patterns` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    likeAllOf(column, patterns) {\n        this.url.searchParams.append(column, `like(all).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches any of `patterns` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    likeAnyOf(column, patterns) {\n        this.url.searchParams.append(column, `like(any).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches `pattern` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param pattern - The pattern to match with\n     */\n    ilike(column, pattern) {\n        this.url.searchParams.append(column, `ilike.${pattern}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches all of `patterns` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    ilikeAllOf(column, patterns) {\n        this.url.searchParams.append(column, `ilike(all).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches any of `patterns` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    ilikeAnyOf(column, patterns) {\n        this.url.searchParams.append(column, `ilike(any).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` IS `value`.\n     *\n     * For non-boolean columns, this is only relevant for checking if the value of\n     * `column` is NULL by setting `value` to `null`.\n     *\n     * For boolean columns, you can also set `value` to `true` or `false` and it\n     * will behave the same way as `.eq()`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    is(column, value) {\n        this.url.searchParams.append(column, `is.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is included in the `values` array.\n     *\n     * @param column - The column to filter on\n     * @param values - The values array to filter with\n     */\n    in(column, values) {\n        const cleanedValues = Array.from(new Set(values))\n            .map((s) => {\n            // handle postgrest reserved characters\n            // https://postgrest.org/en/v7.0.0/api.html#reserved-characters\n            if (typeof s === 'string' && new RegExp('[,()]').test(s))\n                return `\"${s}\"`;\n            else\n                return `${s}`;\n        })\n            .join(',');\n        this.url.searchParams.append(column, `in.(${cleanedValues})`);\n        return this;\n    }\n    /**\n     * Only relevant for jsonb, array, and range columns. Match only rows where\n     * `column` contains every element appearing in `value`.\n     *\n     * @param column - The jsonb, array, or range column to filter on\n     * @param value - The jsonb, array, or range value to filter with\n     */\n    contains(column, value) {\n        if (typeof value === 'string') {\n            // range types can be inclusive '[', ']' or exclusive '(', ')' so just\n            // keep it simple and accept a string\n            this.url.searchParams.append(column, `cs.${value}`);\n        }\n        else if (Array.isArray(value)) {\n            // array\n            this.url.searchParams.append(column, `cs.{${value.join(',')}}`);\n        }\n        else {\n            // json\n            this.url.searchParams.append(column, `cs.${JSON.stringify(value)}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for jsonb, array, and range columns. Match only rows where\n     * every element appearing in `column` is contained by `value`.\n     *\n     * @param column - The jsonb, array, or range column to filter on\n     * @param value - The jsonb, array, or range value to filter with\n     */\n    containedBy(column, value) {\n        if (typeof value === 'string') {\n            // range\n            this.url.searchParams.append(column, `cd.${value}`);\n        }\n        else if (Array.isArray(value)) {\n            // array\n            this.url.searchParams.append(column, `cd.{${value.join(',')}}`);\n        }\n        else {\n            // json\n            this.url.searchParams.append(column, `cd.${JSON.stringify(value)}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is greater than any element in `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeGt(column, range) {\n        this.url.searchParams.append(column, `sr.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is either contained in `range` or greater than any element in\n     * `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeGte(column, range) {\n        this.url.searchParams.append(column, `nxl.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is less than any element in `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeLt(column, range) {\n        this.url.searchParams.append(column, `sl.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is either contained in `range` or less than any element in\n     * `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeLte(column, range) {\n        this.url.searchParams.append(column, `nxr.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where `column` is\n     * mutually exclusive to `range` and there can be no element between the two\n     * ranges.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeAdjacent(column, range) {\n        this.url.searchParams.append(column, `adj.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for array and range columns. Match only rows where\n     * `column` and `value` have an element in common.\n     *\n     * @param column - The array or range column to filter on\n     * @param value - The array or range value to filter with\n     */\n    overlaps(column, value) {\n        if (typeof value === 'string') {\n            // range\n            this.url.searchParams.append(column, `ov.${value}`);\n        }\n        else {\n            // array\n            this.url.searchParams.append(column, `ov.{${value.join(',')}}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for text and tsvector columns. Match only rows where\n     * `column` matches the query string in `query`.\n     *\n     * @param column - The text or tsvector column to filter on\n     * @param query - The query text to match with\n     * @param options - Named parameters\n     * @param options.config - The text search configuration to use\n     * @param options.type - Change how the `query` text is interpreted\n     */\n    textSearch(column, query, { config, type } = {}) {\n        let typePart = '';\n        if (type === 'plain') {\n            typePart = 'pl';\n        }\n        else if (type === 'phrase') {\n            typePart = 'ph';\n        }\n        else if (type === 'websearch') {\n            typePart = 'w';\n        }\n        const configPart = config === undefined ? '' : `(${config})`;\n        this.url.searchParams.append(column, `${typePart}fts${configPart}.${query}`);\n        return this;\n    }\n    /**\n     * Match only rows where each column in `query` keys is equal to its\n     * associated value. Shorthand for multiple `.eq()`s.\n     *\n     * @param query - The object to filter with, with column names as keys mapped\n     * to their filter values\n     */\n    match(query) {\n        Object.entries(query).forEach(([column, value]) => {\n            this.url.searchParams.append(column, `eq.${value}`);\n        });\n        return this;\n    }\n    /**\n     * Match only rows which doesn't satisfy the filter.\n     *\n     * Unlike most filters, `opearator` and `value` are used as-is and need to\n     * follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure they are properly sanitized.\n     *\n     * @param column - The column to filter on\n     * @param operator - The operator to be negated to filter with, following\n     * PostgREST syntax\n     * @param value - The value to filter with, following PostgREST syntax\n     */\n    not(column, operator, value) {\n        this.url.searchParams.append(column, `not.${operator}.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows which satisfy at least one of the filters.\n     *\n     * Unlike most filters, `filters` is used as-is and needs to follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure it's properly sanitized.\n     *\n     * It's currently not possible to do an `.or()` filter across multiple tables.\n     *\n     * @param filters - The filters to use, following PostgREST syntax\n     * @param options - Named parameters\n     * @param options.referencedTable - Set this to filter on referenced tables\n     * instead of the parent table\n     * @param options.foreignTable - Deprecated, use `referencedTable` instead\n     */\n    or(filters, { foreignTable, referencedTable = foreignTable, } = {}) {\n        const key = referencedTable ? `${referencedTable}.or` : 'or';\n        this.url.searchParams.append(key, `(${filters})`);\n        return this;\n    }\n    /**\n     * Match only rows which satisfy the filter. This is an escape hatch - you\n     * should use the specific filter methods wherever possible.\n     *\n     * Unlike most filters, `opearator` and `value` are used as-is and need to\n     * follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure they are properly sanitized.\n     *\n     * @param column - The column to filter on\n     * @param operator - The operator to filter with, following PostgREST syntax\n     * @param value - The value to filter with, following PostgREST syntax\n     */\n    filter(column, operator, value) {\n        this.url.searchParams.append(column, `${operator}.${value}`);\n        return this;\n    }\n}\nexports[\"default\"] = PostgrestFilterBuilder;\n//# sourceMappingURL=PostgrestFilterBuilder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js ***!
  \********************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst PostgrestFilterBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestFilterBuilder */ \"(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\"));\nclass PostgrestQueryBuilder {\n    constructor(url, { headers = {}, schema, fetch, }) {\n        this.url = url;\n        this.headers = headers;\n        this.schema = schema;\n        this.fetch = fetch;\n    }\n    /**\n     * Perform a SELECT query on the table or view.\n     *\n     * @param columns - The columns to retrieve, separated by commas. Columns can be renamed when returned with `customName:columnName`\n     *\n     * @param options - Named parameters\n     *\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     *\n     * @param options.count - Count algorithm to use to count rows in the table or view.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    select(columns, { head = false, count, } = {}) {\n        const method = head ? 'HEAD' : 'GET';\n        // Remove whitespaces except when quoted\n        let quoted = false;\n        const cleanedColumns = (columns !== null && columns !== void 0 ? columns : '*')\n            .split('')\n            .map((c) => {\n            if (/\\s/.test(c) && !quoted) {\n                return '';\n            }\n            if (c === '\"') {\n                quoted = !quoted;\n            }\n            return c;\n        })\n            .join('');\n        this.url.searchParams.set('select', cleanedColumns);\n        if (count) {\n            this.headers['Prefer'] = `count=${count}`;\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n    /**\n     * Perform an INSERT into the table or view.\n     *\n     * By default, inserted rows are not returned. To return it, chain the call\n     * with `.select()`.\n     *\n     * @param values - The values to insert. Pass an object to insert a single row\n     * or an array to insert multiple rows.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.count - Count algorithm to use to count inserted rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     *\n     * @param options.defaultToNull - Make missing fields default to `null`.\n     * Otherwise, use the default value for the column. Only applies for bulk\n     * inserts.\n     */\n    insert(values, { count, defaultToNull = true, } = {}) {\n        const method = 'POST';\n        const prefersHeaders = [];\n        if (this.headers['Prefer']) {\n            prefersHeaders.push(this.headers['Prefer']);\n        }\n        if (count) {\n            prefersHeaders.push(`count=${count}`);\n        }\n        if (!defaultToNull) {\n            prefersHeaders.push('missing=default');\n        }\n        this.headers['Prefer'] = prefersHeaders.join(',');\n        if (Array.isArray(values)) {\n            const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), []);\n            if (columns.length > 0) {\n                const uniqueColumns = [...new Set(columns)].map((column) => `\"${column}\"`);\n                this.url.searchParams.set('columns', uniqueColumns.join(','));\n            }\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            body: values,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n    /**\n     * Perform an UPSERT on the table or view. Depending on the column(s) passed\n     * to `onConflict`, `.upsert()` allows you to perform the equivalent of\n     * `.insert()` if a row with the corresponding `onConflict` columns doesn't\n     * exist, or if it does exist, perform an alternative action depending on\n     * `ignoreDuplicates`.\n     *\n     * By default, upserted rows are not returned. To return it, chain the call\n     * with `.select()`.\n     *\n     * @param values - The values to upsert with. Pass an object to upsert a\n     * single row or an array to upsert multiple rows.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.onConflict - Comma-separated UNIQUE column(s) to specify how\n     * duplicate rows are determined. Two rows are duplicates if all the\n     * `onConflict` columns are equal.\n     *\n     * @param options.ignoreDuplicates - If `true`, duplicate rows are ignored. If\n     * `false`, duplicate rows are merged with existing rows.\n     *\n     * @param options.count - Count algorithm to use to count upserted rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     *\n     * @param options.defaultToNull - Make missing fields default to `null`.\n     * Otherwise, use the default value for the column. This only applies when\n     * inserting new rows, not when merging with existing rows under\n     * `ignoreDuplicates: false`. This also only applies when doing bulk upserts.\n     */\n    upsert(values, { onConflict, ignoreDuplicates = false, count, defaultToNull = true, } = {}) {\n        const method = 'POST';\n        const prefersHeaders = [`resolution=${ignoreDuplicates ? 'ignore' : 'merge'}-duplicates`];\n        if (onConflict !== undefined)\n            this.url.searchParams.set('on_conflict', onConflict);\n        if (this.headers['Prefer']) {\n            prefersHeaders.push(this.headers['Prefer']);\n        }\n        if (count) {\n            prefersHeaders.push(`count=${count}`);\n        }\n        if (!defaultToNull) {\n            prefersHeaders.push('missing=default');\n        }\n        this.headers['Prefer'] = prefersHeaders.join(',');\n        if (Array.isArray(values)) {\n            const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), []);\n            if (columns.length > 0) {\n                const uniqueColumns = [...new Set(columns)].map((column) => `\"${column}\"`);\n                this.url.searchParams.set('columns', uniqueColumns.join(','));\n            }\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            body: values,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n    /**\n     * Perform an UPDATE on the table or view.\n     *\n     * By default, updated rows are not returned. To return it, chain the call\n     * with `.select()` after filters.\n     *\n     * @param values - The values to update with\n     *\n     * @param options - Named parameters\n     *\n     * @param options.count - Count algorithm to use to count updated rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    update(values, { count, } = {}) {\n        const method = 'PATCH';\n        const prefersHeaders = [];\n        if (this.headers['Prefer']) {\n            prefersHeaders.push(this.headers['Prefer']);\n        }\n        if (count) {\n            prefersHeaders.push(`count=${count}`);\n        }\n        this.headers['Prefer'] = prefersHeaders.join(',');\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            body: values,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n    /**\n     * Perform a DELETE on the table or view.\n     *\n     * By default, deleted rows are not returned. To return it, chain the call\n     * with `.select()` after filters.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.count - Count algorithm to use to count deleted rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    delete({ count, } = {}) {\n        const method = 'DELETE';\n        const prefersHeaders = [];\n        if (count) {\n            prefersHeaders.push(`count=${count}`);\n        }\n        if (this.headers['Prefer']) {\n            prefersHeaders.unshift(this.headers['Prefer']);\n        }\n        this.headers['Prefer'] = prefersHeaders.join(',');\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n}\nexports[\"default\"] = PostgrestQueryBuilder;\n//# sourceMappingURL=PostgrestQueryBuilder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js ***!
  \************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst PostgrestBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestBuilder */ \"(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js\"));\nclass PostgrestTransformBuilder extends PostgrestBuilder_1.default {\n    /**\n     * Perform a SELECT on the query result.\n     *\n     * By default, `.insert()`, `.update()`, `.upsert()`, and `.delete()` do not\n     * return modified rows. By calling this method, modified rows are returned in\n     * `data`.\n     *\n     * @param columns - The columns to retrieve, separated by commas\n     */\n    select(columns) {\n        // Remove whitespaces except when quoted\n        let quoted = false;\n        const cleanedColumns = (columns !== null && columns !== void 0 ? columns : '*')\n            .split('')\n            .map((c) => {\n            if (/\\s/.test(c) && !quoted) {\n                return '';\n            }\n            if (c === '\"') {\n                quoted = !quoted;\n            }\n            return c;\n        })\n            .join('');\n        this.url.searchParams.set('select', cleanedColumns);\n        if (this.headers['Prefer']) {\n            this.headers['Prefer'] += ',';\n        }\n        this.headers['Prefer'] += 'return=representation';\n        return this;\n    }\n    /**\n     * Order the query result by `column`.\n     *\n     * You can call this method multiple times to order by multiple columns.\n     *\n     * You can order referenced tables, but it only affects the ordering of the\n     * parent table if you use `!inner` in the query.\n     *\n     * @param column - The column to order by\n     * @param options - Named parameters\n     * @param options.ascending - If `true`, the result will be in ascending order\n     * @param options.nullsFirst - If `true`, `null`s appear first. If `false`,\n     * `null`s appear last.\n     * @param options.referencedTable - Set this to order a referenced table by\n     * its columns\n     * @param options.foreignTable - Deprecated, use `options.referencedTable`\n     * instead\n     */\n    order(column, { ascending = true, nullsFirst, foreignTable, referencedTable = foreignTable, } = {}) {\n        const key = referencedTable ? `${referencedTable}.order` : 'order';\n        const existingOrder = this.url.searchParams.get(key);\n        this.url.searchParams.set(key, `${existingOrder ? `${existingOrder},` : ''}${column}.${ascending ? 'asc' : 'desc'}${nullsFirst === undefined ? '' : nullsFirst ? '.nullsfirst' : '.nullslast'}`);\n        return this;\n    }\n    /**\n     * Limit the query result by `count`.\n     *\n     * @param count - The maximum number of rows to return\n     * @param options - Named parameters\n     * @param options.referencedTable - Set this to limit rows of referenced\n     * tables instead of the parent table\n     * @param options.foreignTable - Deprecated, use `options.referencedTable`\n     * instead\n     */\n    limit(count, { foreignTable, referencedTable = foreignTable, } = {}) {\n        const key = typeof referencedTable === 'undefined' ? 'limit' : `${referencedTable}.limit`;\n        this.url.searchParams.set(key, `${count}`);\n        return this;\n    }\n    /**\n     * Limit the query result by starting at an offset `from` and ending at the offset `to`.\n     * Only records within this range are returned.\n     * This respects the query order and if there is no order clause the range could behave unexpectedly.\n     * The `from` and `to` values are 0-based and inclusive: `range(1, 3)` will include the second, third\n     * and fourth rows of the query.\n     *\n     * @param from - The starting index from which to limit the result\n     * @param to - The last index to which to limit the result\n     * @param options - Named parameters\n     * @param options.referencedTable - Set this to limit rows of referenced\n     * tables instead of the parent table\n     * @param options.foreignTable - Deprecated, use `options.referencedTable`\n     * instead\n     */\n    range(from, to, { foreignTable, referencedTable = foreignTable, } = {}) {\n        const keyOffset = typeof referencedTable === 'undefined' ? 'offset' : `${referencedTable}.offset`;\n        const keyLimit = typeof referencedTable === 'undefined' ? 'limit' : `${referencedTable}.limit`;\n        this.url.searchParams.set(keyOffset, `${from}`);\n        // Range is inclusive, so add 1\n        this.url.searchParams.set(keyLimit, `${to - from + 1}`);\n        return this;\n    }\n    /**\n     * Set the AbortSignal for the fetch request.\n     *\n     * @param signal - The AbortSignal to use for the fetch request\n     */\n    abortSignal(signal) {\n        this.signal = signal;\n        return this;\n    }\n    /**\n     * Return `data` as a single object instead of an array of objects.\n     *\n     * Query result must be one row (e.g. using `.limit(1)`), otherwise this\n     * returns an error.\n     */\n    single() {\n        this.headers['Accept'] = 'application/vnd.pgrst.object+json';\n        return this;\n    }\n    /**\n     * Return `data` as a single object instead of an array of objects.\n     *\n     * Query result must be zero or one row (e.g. using `.limit(1)`), otherwise\n     * this returns an error.\n     */\n    maybeSingle() {\n        // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n        // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n        if (this.method === 'GET') {\n            this.headers['Accept'] = 'application/json';\n        }\n        else {\n            this.headers['Accept'] = 'application/vnd.pgrst.object+json';\n        }\n        this.isMaybeSingle = true;\n        return this;\n    }\n    /**\n     * Return `data` as a string in CSV format.\n     */\n    csv() {\n        this.headers['Accept'] = 'text/csv';\n        return this;\n    }\n    /**\n     * Return `data` as an object in [GeoJSON](https://geojson.org) format.\n     */\n    geojson() {\n        this.headers['Accept'] = 'application/geo+json';\n        return this;\n    }\n    /**\n     * Return `data` as the EXPLAIN plan for the query.\n     *\n     * You need to enable the\n     * [db_plan_enabled](https://supabase.com/docs/guides/database/debugging-performance#enabling-explain)\n     * setting before using this method.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.analyze - If `true`, the query will be executed and the\n     * actual run time will be returned\n     *\n     * @param options.verbose - If `true`, the query identifier will be returned\n     * and `data` will include the output columns of the query\n     *\n     * @param options.settings - If `true`, include information on configuration\n     * parameters that affect query planning\n     *\n     * @param options.buffers - If `true`, include information on buffer usage\n     *\n     * @param options.wal - If `true`, include information on WAL record generation\n     *\n     * @param options.format - The format of the output, can be `\"text\"` (default)\n     * or `\"json\"`\n     */\n    explain({ analyze = false, verbose = false, settings = false, buffers = false, wal = false, format = 'text', } = {}) {\n        var _a;\n        const options = [\n            analyze ? 'analyze' : null,\n            verbose ? 'verbose' : null,\n            settings ? 'settings' : null,\n            buffers ? 'buffers' : null,\n            wal ? 'wal' : null,\n        ]\n            .filter(Boolean)\n            .join('|');\n        // An Accept header can carry multiple media types but postgrest-js always sends one\n        const forMediatype = (_a = this.headers['Accept']) !== null && _a !== void 0 ? _a : 'application/json';\n        this.headers['Accept'] = `application/vnd.pgrst.plan+${format}; for=\"${forMediatype}\"; options=${options};`;\n        if (format === 'json')\n            return this;\n        else\n            return this;\n    }\n    /**\n     * Rollback the query.\n     *\n     * `data` will still be returned, but the query is not committed.\n     */\n    rollback() {\n        var _a;\n        if (((_a = this.headers['Prefer']) !== null && _a !== void 0 ? _a : '').trim().length > 0) {\n            this.headers['Prefer'] += ',tx=rollback';\n        }\n        else {\n            this.headers['Prefer'] = 'tx=rollback';\n        }\n        return this;\n    }\n    /**\n     * Override the type of the returned `data`.\n     *\n     * @typeParam NewResult - The new result type to override with\n     * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n     */\n    returns() {\n        return this;\n    }\n}\nexports[\"default\"] = PostgrestTransformBuilder;\n//# sourceMappingURL=PostgrestTransformBuilder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/constants.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/constants.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DEFAULT_HEADERS = void 0;\nconst version_1 = __webpack_require__(/*! ./version */ \"(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/version.js\");\nexports.DEFAULT_HEADERS = { 'X-Client-Info': `postgrest-js/${version_1.version}` };\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9Ac3VwYWJhc2UrcG9zdGdyZXN0LWpzQDEuMTkuNC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Bvc3RncmVzdC1qcy9kaXN0L2Nqcy9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsdUJBQXVCO0FBQ3ZCLGtCQUFrQixtQkFBTyxDQUFDLDhJQUFXO0FBQ3JDLHVCQUF1QixLQUFLLGlDQUFpQyxrQkFBa0I7QUFDL0UiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca29tYWxcXERvY3VtZW50c1xcanNtLWFwcHNcXG5leHRcXGVjb21cXHYwLWRvd25sb2FkLWF0dGVtcHRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStwb3N0Z3Jlc3QtanNAMS4xOS40XFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxccG9zdGdyZXN0LWpzXFxkaXN0XFxjanNcXGNvbnN0YW50cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuREVGQVVMVF9IRUFERVJTID0gdm9pZCAwO1xuY29uc3QgdmVyc2lvbl8xID0gcmVxdWlyZShcIi4vdmVyc2lvblwiKTtcbmV4cG9ydHMuREVGQVVMVF9IRUFERVJTID0geyAnWC1DbGllbnQtSW5mbyc6IGBwb3N0Z3Jlc3QtanMvJHt2ZXJzaW9uXzEudmVyc2lvbn1gIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb25zdGFudHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/constants.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/index.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/index.js ***!
  \****************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PostgrestError = exports.PostgrestBuilder = exports.PostgrestTransformBuilder = exports.PostgrestFilterBuilder = exports.PostgrestQueryBuilder = exports.PostgrestClient = void 0;\n// Always update wrapper.mjs when updating this file.\nconst PostgrestClient_1 = __importDefault(__webpack_require__(/*! ./PostgrestClient */ \"(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js\"));\nexports.PostgrestClient = PostgrestClient_1.default;\nconst PostgrestQueryBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestQueryBuilder */ \"(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js\"));\nexports.PostgrestQueryBuilder = PostgrestQueryBuilder_1.default;\nconst PostgrestFilterBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestFilterBuilder */ \"(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\"));\nexports.PostgrestFilterBuilder = PostgrestFilterBuilder_1.default;\nconst PostgrestTransformBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestTransformBuilder */ \"(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js\"));\nexports.PostgrestTransformBuilder = PostgrestTransformBuilder_1.default;\nconst PostgrestBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestBuilder */ \"(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js\"));\nexports.PostgrestBuilder = PostgrestBuilder_1.default;\nconst PostgrestError_1 = __importDefault(__webpack_require__(/*! ./PostgrestError */ \"(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js\"));\nexports.PostgrestError = PostgrestError_1.default;\nexports[\"default\"] = {\n    PostgrestClient: PostgrestClient_1.default,\n    PostgrestQueryBuilder: PostgrestQueryBuilder_1.default,\n    PostgrestFilterBuilder: PostgrestFilterBuilder_1.default,\n    PostgrestTransformBuilder: PostgrestTransformBuilder_1.default,\n    PostgrestBuilder: PostgrestBuilder_1.default,\n    PostgrestError: PostgrestError_1.default,\n};\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/version.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/version.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.version = void 0;\nexports.version = '0.0.0-automated';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9Ac3VwYWJhc2UrcG9zdGdyZXN0LWpzQDEuMTkuNC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Bvc3RncmVzdC1qcy9kaXN0L2Nqcy92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGVBQWU7QUFDZixlQUFlO0FBQ2YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca29tYWxcXERvY3VtZW50c1xcanNtLWFwcHNcXG5leHRcXGVjb21cXHYwLWRvd25sb2FkLWF0dGVtcHRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStwb3N0Z3Jlc3QtanNAMS4xOS40XFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxccG9zdGdyZXN0LWpzXFxkaXN0XFxjanNcXHZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLnZlcnNpb24gPSB2b2lkIDA7XG5leHBvcnRzLnZlcnNpb24gPSAnMC4wLjAtYXV0b21hdGVkJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/version.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostgrestBuilder: () => (/* binding */ PostgrestBuilder),\n/* harmony export */   PostgrestClient: () => (/* binding */ PostgrestClient),\n/* harmony export */   PostgrestError: () => (/* binding */ PostgrestError),\n/* harmony export */   PostgrestFilterBuilder: () => (/* binding */ PostgrestFilterBuilder),\n/* harmony export */   PostgrestQueryBuilder: () => (/* binding */ PostgrestQueryBuilder),\n/* harmony export */   PostgrestTransformBuilder: () => (/* binding */ PostgrestTransformBuilder),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _cjs_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../cjs/index.js */ \"(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/index.js\");\n\nconst {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError,\n} = _cjs_index_js__WEBPACK_IMPORTED_MODULE_0__\n\n\n\n// compatibility with CJS output\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError,\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9Ac3VwYWJhc2UrcG9zdGdyZXN0LWpzQDEuMTkuNC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Bvc3RncmVzdC1qcy9kaXN0L2VzbS93cmFwcGVyLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFtQztBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUUsRUFBRSwwQ0FBSzs7QUFTUjs7QUFFRDtBQUNBLGlFQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxrb21hbFxcRG9jdW1lbnRzXFxqc20tYXBwc1xcbmV4dFxcZWNvbVxcdjAtZG93bmxvYWQtYXR0ZW1wdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK3Bvc3RncmVzdC1qc0AxLjE5LjRcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxwb3N0Z3Jlc3QtanNcXGRpc3RcXGVzbVxcd3JhcHBlci5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGluZGV4IGZyb20gJy4uL2Nqcy9pbmRleC5qcydcbmNvbnN0IHtcbiAgUG9zdGdyZXN0Q2xpZW50LFxuICBQb3N0Z3Jlc3RRdWVyeUJ1aWxkZXIsXG4gIFBvc3RncmVzdEZpbHRlckJ1aWxkZXIsXG4gIFBvc3RncmVzdFRyYW5zZm9ybUJ1aWxkZXIsXG4gIFBvc3RncmVzdEJ1aWxkZXIsXG4gIFBvc3RncmVzdEVycm9yLFxufSA9IGluZGV4XG5cbmV4cG9ydCB7XG4gIFBvc3RncmVzdEJ1aWxkZXIsXG4gIFBvc3RncmVzdENsaWVudCxcbiAgUG9zdGdyZXN0RmlsdGVyQnVpbGRlcixcbiAgUG9zdGdyZXN0UXVlcnlCdWlsZGVyLFxuICBQb3N0Z3Jlc3RUcmFuc2Zvcm1CdWlsZGVyLFxuICBQb3N0Z3Jlc3RFcnJvcixcbn1cblxuLy8gY29tcGF0aWJpbGl0eSB3aXRoIENKUyBvdXRwdXRcbmV4cG9ydCBkZWZhdWx0IHtcbiAgUG9zdGdyZXN0Q2xpZW50LFxuICBQb3N0Z3Jlc3RRdWVyeUJ1aWxkZXIsXG4gIFBvc3RncmVzdEZpbHRlckJ1aWxkZXIsXG4gIFBvc3RncmVzdFRyYW5zZm9ybUJ1aWxkZXIsXG4gIFBvc3RncmVzdEJ1aWxkZXIsXG4gIFBvc3RncmVzdEVycm9yLFxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs\n");

/***/ })

};
;