"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+realtime-js@2.15.0";
exports.ids = ["vendor-chunks/@supabase+realtime-js@2.15.0"];
exports.modules = {

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   REALTIME_CHANNEL_STATES: () => (/* binding */ REALTIME_CHANNEL_STATES),\n/* harmony export */   REALTIME_LISTEN_TYPES: () => (/* binding */ REALTIME_LISTEN_TYPES),\n/* harmony export */   REALTIME_POSTGRES_CHANGES_LISTEN_EVENT: () => (/* binding */ REALTIME_POSTGRES_CHANGES_LISTEN_EVENT),\n/* harmony export */   REALTIME_SUBSCRIBE_STATES: () => (/* binding */ REALTIME_SUBSCRIBE_STATES),\n/* harmony export */   \"default\": () => (/* binding */ RealtimeChannel)\n/* harmony export */ });\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/constants */ \"(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_push__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/push */ \"(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/push.js\");\n/* harmony import */ var _lib_timer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/timer */ \"(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/timer.js\");\n/* harmony import */ var _RealtimePresence__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./RealtimePresence */ \"(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js\");\n/* harmony import */ var _lib_transformers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/transformers */ \"(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js\");\n\n\n\n\n\n\nvar REALTIME_POSTGRES_CHANGES_LISTEN_EVENT;\n(function (REALTIME_POSTGRES_CHANGES_LISTEN_EVENT) {\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"ALL\"] = \"*\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"INSERT\"] = \"INSERT\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"UPDATE\"] = \"UPDATE\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"DELETE\"] = \"DELETE\";\n})(REALTIME_POSTGRES_CHANGES_LISTEN_EVENT || (REALTIME_POSTGRES_CHANGES_LISTEN_EVENT = {}));\nvar REALTIME_LISTEN_TYPES;\n(function (REALTIME_LISTEN_TYPES) {\n    REALTIME_LISTEN_TYPES[\"BROADCAST\"] = \"broadcast\";\n    REALTIME_LISTEN_TYPES[\"PRESENCE\"] = \"presence\";\n    REALTIME_LISTEN_TYPES[\"POSTGRES_CHANGES\"] = \"postgres_changes\";\n    REALTIME_LISTEN_TYPES[\"SYSTEM\"] = \"system\";\n})(REALTIME_LISTEN_TYPES || (REALTIME_LISTEN_TYPES = {}));\nvar REALTIME_SUBSCRIBE_STATES;\n(function (REALTIME_SUBSCRIBE_STATES) {\n    REALTIME_SUBSCRIBE_STATES[\"SUBSCRIBED\"] = \"SUBSCRIBED\";\n    REALTIME_SUBSCRIBE_STATES[\"TIMED_OUT\"] = \"TIMED_OUT\";\n    REALTIME_SUBSCRIBE_STATES[\"CLOSED\"] = \"CLOSED\";\n    REALTIME_SUBSCRIBE_STATES[\"CHANNEL_ERROR\"] = \"CHANNEL_ERROR\";\n})(REALTIME_SUBSCRIBE_STATES || (REALTIME_SUBSCRIBE_STATES = {}));\nconst REALTIME_CHANNEL_STATES = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES;\n/** A channel is the basic building block of Realtime\n * and narrows the scope of data flow to subscribed clients.\n * You can think of a channel as a chatroom where participants are able to see who's online\n * and send and receive messages.\n */\nclass RealtimeChannel {\n    constructor(\n    /** Topic name can be any string. */\n    topic, params = { config: {} }, socket) {\n        this.topic = topic;\n        this.params = params;\n        this.socket = socket;\n        this.bindings = {};\n        this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.closed;\n        this.joinedOnce = false;\n        this.pushBuffer = [];\n        this.subTopic = topic.replace(/^realtime:/i, '');\n        this.params.config = Object.assign({\n            broadcast: { ack: false, self: false },\n            presence: { key: '', enabled: false },\n            private: false,\n        }, params.config);\n        this.timeout = this.socket.timeout;\n        this.joinPush = new _lib_push__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this, _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.join, this.params, this.timeout);\n        this.rejoinTimer = new _lib_timer__WEBPACK_IMPORTED_MODULE_2__[\"default\"](() => this._rejoinUntilConnected(), this.socket.reconnectAfterMs);\n        this.joinPush.receive('ok', () => {\n            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.joined;\n            this.rejoinTimer.reset();\n            this.pushBuffer.forEach((pushEvent) => pushEvent.send());\n            this.pushBuffer = [];\n        });\n        this._onClose(() => {\n            this.rejoinTimer.reset();\n            this.socket.log('channel', `close ${this.topic} ${this._joinRef()}`);\n            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.closed;\n            this.socket._remove(this);\n        });\n        this._onError((reason) => {\n            if (this._isLeaving() || this._isClosed()) {\n                return;\n            }\n            this.socket.log('channel', `error ${this.topic}`, reason);\n            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.errored;\n            this.rejoinTimer.scheduleTimeout();\n        });\n        this.joinPush.receive('timeout', () => {\n            if (!this._isJoining()) {\n                return;\n            }\n            this.socket.log('channel', `timeout ${this.topic}`, this.joinPush.timeout);\n            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.errored;\n            this.rejoinTimer.scheduleTimeout();\n        });\n        this.joinPush.receive('error', (reason) => {\n            if (this._isLeaving() || this._isClosed()) {\n                return;\n            }\n            this.socket.log('channel', `error ${this.topic}`, reason);\n            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.errored;\n            this.rejoinTimer.scheduleTimeout();\n        });\n        this._on(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.reply, {}, (payload, ref) => {\n            this._trigger(this._replyEventName(ref), payload);\n        });\n        this.presence = new _RealtimePresence__WEBPACK_IMPORTED_MODULE_3__[\"default\"](this);\n        this.broadcastEndpointURL = (0,_lib_transformers__WEBPACK_IMPORTED_MODULE_4__.httpEndpointURL)(this.socket.endPoint);\n        this.private = this.params.config.private || false;\n    }\n    /** Subscribe registers your client with the server */\n    subscribe(callback, timeout = this.timeout) {\n        var _a, _b;\n        if (!this.socket.isConnected()) {\n            this.socket.connect();\n        }\n        if (this.state == _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.closed) {\n            const { config: { broadcast, presence, private: isPrivate }, } = this.params;\n            const postgres_changes = (_b = (_a = this.bindings.postgres_changes) === null || _a === void 0 ? void 0 : _a.map((r) => r.filter)) !== null && _b !== void 0 ? _b : [];\n            const presence_enabled = !!this.bindings[REALTIME_LISTEN_TYPES.PRESENCE] &&\n                this.bindings[REALTIME_LISTEN_TYPES.PRESENCE].length > 0;\n            const accessTokenPayload = {};\n            const config = {\n                broadcast,\n                presence: Object.assign(Object.assign({}, presence), { enabled: presence_enabled }),\n                postgres_changes,\n                private: isPrivate,\n            };\n            if (this.socket.accessTokenValue) {\n                accessTokenPayload.access_token = this.socket.accessTokenValue;\n            }\n            this._onError((e) => callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, e));\n            this._onClose(() => callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CLOSED));\n            this.updateJoinPayload(Object.assign({ config }, accessTokenPayload));\n            this.joinedOnce = true;\n            this._rejoin(timeout);\n            this.joinPush\n                .receive('ok', async ({ postgres_changes }) => {\n                var _a;\n                this.socket.setAuth();\n                if (postgres_changes === undefined) {\n                    callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);\n                    return;\n                }\n                else {\n                    const clientPostgresBindings = this.bindings.postgres_changes;\n                    const bindingsLen = (_a = clientPostgresBindings === null || clientPostgresBindings === void 0 ? void 0 : clientPostgresBindings.length) !== null && _a !== void 0 ? _a : 0;\n                    const newPostgresBindings = [];\n                    for (let i = 0; i < bindingsLen; i++) {\n                        const clientPostgresBinding = clientPostgresBindings[i];\n                        const { filter: { event, schema, table, filter }, } = clientPostgresBinding;\n                        const serverPostgresFilter = postgres_changes && postgres_changes[i];\n                        if (serverPostgresFilter &&\n                            serverPostgresFilter.event === event &&\n                            serverPostgresFilter.schema === schema &&\n                            serverPostgresFilter.table === table &&\n                            serverPostgresFilter.filter === filter) {\n                            newPostgresBindings.push(Object.assign(Object.assign({}, clientPostgresBinding), { id: serverPostgresFilter.id }));\n                        }\n                        else {\n                            this.unsubscribe();\n                            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.errored;\n                            callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, new Error('mismatch between server and client bindings for postgres changes'));\n                            return;\n                        }\n                    }\n                    this.bindings.postgres_changes = newPostgresBindings;\n                    callback && callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);\n                    return;\n                }\n            })\n                .receive('error', (error) => {\n                this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.errored;\n                callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, new Error(JSON.stringify(Object.values(error).join(', ') || 'error')));\n                return;\n            })\n                .receive('timeout', () => {\n                callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.TIMED_OUT);\n                return;\n            });\n        }\n        return this;\n    }\n    presenceState() {\n        return this.presence.state;\n    }\n    async track(payload, opts = {}) {\n        return await this.send({\n            type: 'presence',\n            event: 'track',\n            payload,\n        }, opts.timeout || this.timeout);\n    }\n    async untrack(opts = {}) {\n        return await this.send({\n            type: 'presence',\n            event: 'untrack',\n        }, opts);\n    }\n    on(type, filter, callback) {\n        if (this.state === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.joined &&\n            type === REALTIME_LISTEN_TYPES.PRESENCE) {\n            this.socket.log('channel', `resubscribe to ${this.topic} due to change in presence callbacks on joined channel`);\n            this.unsubscribe().then(() => this.subscribe());\n        }\n        return this._on(type, filter, callback);\n    }\n    /**\n     * Sends a message into the channel.\n     *\n     * @param args Arguments to send to channel\n     * @param args.type The type of event to send\n     * @param args.event The name of the event being sent\n     * @param args.payload Payload to be sent\n     * @param opts Options to be used during the send process\n     */\n    async send(args, opts = {}) {\n        var _a, _b;\n        if (!this._canPush() && args.type === 'broadcast') {\n            const { event, payload: endpoint_payload } = args;\n            const authorization = this.socket.accessTokenValue\n                ? `Bearer ${this.socket.accessTokenValue}`\n                : '';\n            const options = {\n                method: 'POST',\n                headers: {\n                    Authorization: authorization,\n                    apikey: this.socket.apiKey ? this.socket.apiKey : '',\n                    'Content-Type': 'application/json',\n                },\n                body: JSON.stringify({\n                    messages: [\n                        {\n                            topic: this.subTopic,\n                            event,\n                            payload: endpoint_payload,\n                            private: this.private,\n                        },\n                    ],\n                }),\n            };\n            try {\n                const response = await this._fetchWithTimeout(this.broadcastEndpointURL, options, (_a = opts.timeout) !== null && _a !== void 0 ? _a : this.timeout);\n                await ((_b = response.body) === null || _b === void 0 ? void 0 : _b.cancel());\n                return response.ok ? 'ok' : 'error';\n            }\n            catch (error) {\n                if (error.name === 'AbortError') {\n                    return 'timed out';\n                }\n                else {\n                    return 'error';\n                }\n            }\n        }\n        else {\n            return new Promise((resolve) => {\n                var _a, _b, _c;\n                const push = this._push(args.type, args, opts.timeout || this.timeout);\n                if (args.type === 'broadcast' && !((_c = (_b = (_a = this.params) === null || _a === void 0 ? void 0 : _a.config) === null || _b === void 0 ? void 0 : _b.broadcast) === null || _c === void 0 ? void 0 : _c.ack)) {\n                    resolve('ok');\n                }\n                push.receive('ok', () => resolve('ok'));\n                push.receive('error', () => resolve('error'));\n                push.receive('timeout', () => resolve('timed out'));\n            });\n        }\n    }\n    updateJoinPayload(payload) {\n        this.joinPush.updatePayload(payload);\n    }\n    /**\n     * Leaves the channel.\n     *\n     * Unsubscribes from server events, and instructs channel to terminate on server.\n     * Triggers onClose() hooks.\n     *\n     * To receive leave acknowledgements, use the a `receive` hook to bind to the server ack, ie:\n     * channel.unsubscribe().receive(\"ok\", () => alert(\"left!\") )\n     */\n    unsubscribe(timeout = this.timeout) {\n        this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.leaving;\n        const onClose = () => {\n            this.socket.log('channel', `leave ${this.topic}`);\n            this._trigger(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.close, 'leave', this._joinRef());\n        };\n        this.joinPush.destroy();\n        let leavePush = null;\n        return new Promise((resolve) => {\n            leavePush = new _lib_push__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this, _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.leave, {}, timeout);\n            leavePush\n                .receive('ok', () => {\n                onClose();\n                resolve('ok');\n            })\n                .receive('timeout', () => {\n                onClose();\n                resolve('timed out');\n            })\n                .receive('error', () => {\n                resolve('error');\n            });\n            leavePush.send();\n            if (!this._canPush()) {\n                leavePush.trigger('ok', {});\n            }\n        }).finally(() => {\n            leavePush === null || leavePush === void 0 ? void 0 : leavePush.destroy();\n        });\n    }\n    /**\n     * Teardown the channel.\n     *\n     * Destroys and stops related timers.\n     */\n    teardown() {\n        this.pushBuffer.forEach((push) => push.destroy());\n        this.pushBuffer = [];\n        this.rejoinTimer.reset();\n        this.joinPush.destroy();\n        this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.closed;\n        this.bindings = {};\n    }\n    /** @internal */\n    async _fetchWithTimeout(url, options, timeout) {\n        const controller = new AbortController();\n        const id = setTimeout(() => controller.abort(), timeout);\n        const response = await this.socket.fetch(url, Object.assign(Object.assign({}, options), { signal: controller.signal }));\n        clearTimeout(id);\n        return response;\n    }\n    /** @internal */\n    _push(event, payload, timeout = this.timeout) {\n        if (!this.joinedOnce) {\n            throw `tried to push '${event}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;\n        }\n        let pushEvent = new _lib_push__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this, event, payload, timeout);\n        if (this._canPush()) {\n            pushEvent.send();\n        }\n        else {\n            this._addToPushBuffer(pushEvent);\n        }\n        return pushEvent;\n    }\n    /** @internal */\n    _addToPushBuffer(pushEvent) {\n        pushEvent.startTimeout();\n        this.pushBuffer.push(pushEvent);\n        // Enforce buffer size limit\n        if (this.pushBuffer.length > _lib_constants__WEBPACK_IMPORTED_MODULE_0__.MAX_PUSH_BUFFER_SIZE) {\n            const removedPush = this.pushBuffer.shift();\n            if (removedPush) {\n                removedPush.destroy();\n                this.socket.log('channel', `discarded push due to buffer overflow: ${removedPush.event}`, removedPush.payload);\n            }\n        }\n    }\n    /**\n     * Overridable message hook\n     *\n     * Receives all events for specialized message handling before dispatching to the channel callbacks.\n     * Must return the payload, modified or unmodified.\n     *\n     * @internal\n     */\n    _onMessage(_event, payload, _ref) {\n        return payload;\n    }\n    /** @internal */\n    _isMember(topic) {\n        return this.topic === topic;\n    }\n    /** @internal */\n    _joinRef() {\n        return this.joinPush.ref;\n    }\n    /** @internal */\n    _trigger(type, payload, ref) {\n        var _a, _b;\n        const typeLower = type.toLocaleLowerCase();\n        const { close, error, leave, join } = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS;\n        const events = [close, error, leave, join];\n        if (ref && events.indexOf(typeLower) >= 0 && ref !== this._joinRef()) {\n            return;\n        }\n        let handledPayload = this._onMessage(typeLower, payload, ref);\n        if (payload && !handledPayload) {\n            throw 'channel onMessage callbacks must return the payload, modified or unmodified';\n        }\n        if (['insert', 'update', 'delete'].includes(typeLower)) {\n            (_a = this.bindings.postgres_changes) === null || _a === void 0 ? void 0 : _a.filter((bind) => {\n                var _a, _b, _c;\n                return (((_a = bind.filter) === null || _a === void 0 ? void 0 : _a.event) === '*' ||\n                    ((_c = (_b = bind.filter) === null || _b === void 0 ? void 0 : _b.event) === null || _c === void 0 ? void 0 : _c.toLocaleLowerCase()) === typeLower);\n            }).map((bind) => bind.callback(handledPayload, ref));\n        }\n        else {\n            (_b = this.bindings[typeLower]) === null || _b === void 0 ? void 0 : _b.filter((bind) => {\n                var _a, _b, _c, _d, _e, _f;\n                if (['broadcast', 'presence', 'postgres_changes'].includes(typeLower)) {\n                    if ('id' in bind) {\n                        const bindId = bind.id;\n                        const bindEvent = (_a = bind.filter) === null || _a === void 0 ? void 0 : _a.event;\n                        return (bindId &&\n                            ((_b = payload.ids) === null || _b === void 0 ? void 0 : _b.includes(bindId)) &&\n                            (bindEvent === '*' ||\n                                (bindEvent === null || bindEvent === void 0 ? void 0 : bindEvent.toLocaleLowerCase()) ===\n                                    ((_c = payload.data) === null || _c === void 0 ? void 0 : _c.type.toLocaleLowerCase())));\n                    }\n                    else {\n                        const bindEvent = (_e = (_d = bind === null || bind === void 0 ? void 0 : bind.filter) === null || _d === void 0 ? void 0 : _d.event) === null || _e === void 0 ? void 0 : _e.toLocaleLowerCase();\n                        return (bindEvent === '*' ||\n                            bindEvent === ((_f = payload === null || payload === void 0 ? void 0 : payload.event) === null || _f === void 0 ? void 0 : _f.toLocaleLowerCase()));\n                    }\n                }\n                else {\n                    return bind.type.toLocaleLowerCase() === typeLower;\n                }\n            }).map((bind) => {\n                if (typeof handledPayload === 'object' && 'ids' in handledPayload) {\n                    const postgresChanges = handledPayload.data;\n                    const { schema, table, commit_timestamp, type, errors } = postgresChanges;\n                    const enrichedPayload = {\n                        schema: schema,\n                        table: table,\n                        commit_timestamp: commit_timestamp,\n                        eventType: type,\n                        new: {},\n                        old: {},\n                        errors: errors,\n                    };\n                    handledPayload = Object.assign(Object.assign({}, enrichedPayload), this._getPayloadRecords(postgresChanges));\n                }\n                bind.callback(handledPayload, ref);\n            });\n        }\n    }\n    /** @internal */\n    _isClosed() {\n        return this.state === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.closed;\n    }\n    /** @internal */\n    _isJoined() {\n        return this.state === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.joined;\n    }\n    /** @internal */\n    _isJoining() {\n        return this.state === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.joining;\n    }\n    /** @internal */\n    _isLeaving() {\n        return this.state === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.leaving;\n    }\n    /** @internal */\n    _replyEventName(ref) {\n        return `chan_reply_${ref}`;\n    }\n    /** @internal */\n    _on(type, filter, callback) {\n        const typeLower = type.toLocaleLowerCase();\n        const binding = {\n            type: typeLower,\n            filter: filter,\n            callback: callback,\n        };\n        if (this.bindings[typeLower]) {\n            this.bindings[typeLower].push(binding);\n        }\n        else {\n            this.bindings[typeLower] = [binding];\n        }\n        return this;\n    }\n    /** @internal */\n    _off(type, filter) {\n        const typeLower = type.toLocaleLowerCase();\n        if (this.bindings[typeLower]) {\n            this.bindings[typeLower] = this.bindings[typeLower].filter((bind) => {\n                var _a;\n                return !(((_a = bind.type) === null || _a === void 0 ? void 0 : _a.toLocaleLowerCase()) === typeLower &&\n                    RealtimeChannel.isEqual(bind.filter, filter));\n            });\n        }\n        return this;\n    }\n    /** @internal */\n    static isEqual(obj1, obj2) {\n        if (Object.keys(obj1).length !== Object.keys(obj2).length) {\n            return false;\n        }\n        for (const k in obj1) {\n            if (obj1[k] !== obj2[k]) {\n                return false;\n            }\n        }\n        return true;\n    }\n    /** @internal */\n    _rejoinUntilConnected() {\n        this.rejoinTimer.scheduleTimeout();\n        if (this.socket.isConnected()) {\n            this._rejoin();\n        }\n    }\n    /**\n     * Registers a callback that will be executed when the channel closes.\n     *\n     * @internal\n     */\n    _onClose(callback) {\n        this._on(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.close, {}, callback);\n    }\n    /**\n     * Registers a callback that will be executed when the channel encounteres an error.\n     *\n     * @internal\n     */\n    _onError(callback) {\n        this._on(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.error, {}, (reason) => callback(reason));\n    }\n    /**\n     * Returns `true` if the socket is connected and the channel has been joined.\n     *\n     * @internal\n     */\n    _canPush() {\n        return this.socket.isConnected() && this._isJoined();\n    }\n    /** @internal */\n    _rejoin(timeout = this.timeout) {\n        if (this._isLeaving()) {\n            return;\n        }\n        this.socket._leaveOpenTopic(this.topic);\n        this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.joining;\n        this.joinPush.resend(timeout);\n    }\n    /** @internal */\n    _getPayloadRecords(payload) {\n        const records = {\n            new: {},\n            old: {},\n        };\n        if (payload.type === 'INSERT' || payload.type === 'UPDATE') {\n            records.new = _lib_transformers__WEBPACK_IMPORTED_MODULE_4__.convertChangeData(payload.columns, payload.record);\n        }\n        if (payload.type === 'UPDATE' || payload.type === 'DELETE') {\n            records.old = _lib_transformers__WEBPACK_IMPORTED_MODULE_4__.convertChangeData(payload.columns, payload.old_record);\n        }\n        return records;\n    }\n}\n//# sourceMappingURL=RealtimeChannel.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RealtimeClient)\n/* harmony export */ });\n/* harmony import */ var _lib_websocket_factory__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/websocket-factory */ \"(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/websocket-factory.js\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/constants */ \"(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_serializer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/serializer */ \"(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/serializer.js\");\n/* harmony import */ var _lib_timer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/timer */ \"(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/timer.js\");\n/* harmony import */ var _lib_transformers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/transformers */ \"(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js\");\n/* harmony import */ var _RealtimeChannel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./RealtimeChannel */ \"(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js\");\n\n\n\n\n\n\nconst noop = () => { };\n// Connection-related constants\nconst CONNECTION_TIMEOUTS = {\n    HEARTBEAT_INTERVAL: 25000,\n    RECONNECT_DELAY: 10,\n    HEARTBEAT_TIMEOUT_FALLBACK: 100,\n};\nconst RECONNECT_INTERVALS = [1000, 2000, 5000, 10000];\nconst DEFAULT_RECONNECT_FALLBACK = 10000;\nconst WORKER_SCRIPT = `\n  addEventListener(\"message\", (e) => {\n    if (e.data.event === \"start\") {\n      setInterval(() => postMessage({ event: \"keepAlive\" }), e.data.interval);\n    }\n  });`;\nclass RealtimeClient {\n    /**\n     * Initializes the Socket.\n     *\n     * @param endPoint The string WebSocket endpoint, ie, \"ws://example.com/socket\", \"wss://example.com\", \"/socket\" (inherited host & protocol)\n     * @param httpEndpoint The string HTTP endpoint, ie, \"https://example.com\", \"/\" (inherited host & protocol)\n     * @param options.transport The Websocket Transport, for example WebSocket. This can be a custom implementation\n     * @param options.timeout The default timeout in milliseconds to trigger push timeouts.\n     * @param options.params The optional params to pass when connecting.\n     * @param options.headers Deprecated: headers cannot be set on websocket connections and this option will be removed in the future.\n     * @param options.heartbeatIntervalMs The millisec interval to send a heartbeat message.\n     * @param options.logger The optional function for specialized logging, ie: logger: (kind, msg, data) => { console.log(`${kind}: ${msg}`, data) }\n     * @param options.logLevel Sets the log level for Realtime\n     * @param options.encode The function to encode outgoing messages. Defaults to JSON: (payload, callback) => callback(JSON.stringify(payload))\n     * @param options.decode The function to decode incoming messages. Defaults to Serializer's decode.\n     * @param options.reconnectAfterMs he optional function that returns the millsec reconnect interval. Defaults to stepped backoff off.\n     * @param options.worker Use Web Worker to set a side flow. Defaults to false.\n     * @param options.workerUrl The URL of the worker script. Defaults to https://realtime.supabase.com/worker.js that includes a heartbeat event call to keep the connection alive.\n     */\n    constructor(endPoint, options) {\n        var _a;\n        this.accessTokenValue = null;\n        this.apiKey = null;\n        this.channels = new Array();\n        this.endPoint = '';\n        this.httpEndpoint = '';\n        /** @deprecated headers cannot be set on websocket connections */\n        this.headers = {};\n        this.params = {};\n        this.timeout = _lib_constants__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_TIMEOUT;\n        this.transport = null;\n        this.heartbeatIntervalMs = CONNECTION_TIMEOUTS.HEARTBEAT_INTERVAL;\n        this.heartbeatTimer = undefined;\n        this.pendingHeartbeatRef = null;\n        this.heartbeatCallback = noop;\n        this.ref = 0;\n        this.reconnectTimer = null;\n        this.logger = noop;\n        this.conn = null;\n        this.sendBuffer = [];\n        this.serializer = new _lib_serializer__WEBPACK_IMPORTED_MODULE_2__[\"default\"]();\n        this.stateChangeCallbacks = {\n            open: [],\n            close: [],\n            error: [],\n            message: [],\n        };\n        this.accessToken = null;\n        this._connectionState = 'disconnected';\n        this._wasManualDisconnect = false;\n        this._authPromise = null;\n        /**\n         * Use either custom fetch, if provided, or default fetch to make HTTP requests\n         *\n         * @internal\n         */\n        this._resolveFetch = (customFetch) => {\n            let _fetch;\n            if (customFetch) {\n                _fetch = customFetch;\n            }\n            else if (typeof fetch === 'undefined') {\n                // Node.js environment without native fetch\n                _fetch = (...args) => Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! @supabase/node-fetch */ \"(action-browser)/./node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\", 23))\n                    .then(({ default: fetch }) => fetch(...args))\n                    .catch((error) => {\n                    throw new Error(`Failed to load @supabase/node-fetch: ${error.message}. ` +\n                        `This is required for HTTP requests in Node.js environments without native fetch.`);\n                });\n            }\n            else {\n                _fetch = fetch;\n            }\n            return (...args) => _fetch(...args);\n        };\n        // Validate required parameters\n        if (!((_a = options === null || options === void 0 ? void 0 : options.params) === null || _a === void 0 ? void 0 : _a.apikey)) {\n            throw new Error('API key is required to connect to Realtime');\n        }\n        this.apiKey = options.params.apikey;\n        // Initialize endpoint URLs\n        this.endPoint = `${endPoint}/${_lib_constants__WEBPACK_IMPORTED_MODULE_1__.TRANSPORTS.websocket}`;\n        this.httpEndpoint = (0,_lib_transformers__WEBPACK_IMPORTED_MODULE_4__.httpEndpointURL)(endPoint);\n        this._initializeOptions(options);\n        this._setupReconnectionTimer();\n        this.fetch = this._resolveFetch(options === null || options === void 0 ? void 0 : options.fetch);\n    }\n    /**\n     * Connects the socket, unless already connected.\n     */\n    connect() {\n        // Skip if already connecting, disconnecting, or connected\n        if (this.isConnecting() ||\n            this.isDisconnecting() ||\n            (this.conn !== null && this.isConnected())) {\n            return;\n        }\n        this._setConnectionState('connecting');\n        this._setAuthSafely('connect');\n        // Establish WebSocket connection\n        if (!this.transport) {\n            try {\n                this.conn = _lib_websocket_factory__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createWebSocket(this.endpointURL());\n            }\n            catch (error) {\n                this._setConnectionState('disconnected');\n                throw new Error(`WebSocket not available: ${error.message}`);\n            }\n        }\n        else {\n            // Use custom transport if provided\n            this.conn = new this.transport(this.endpointURL());\n        }\n        this._setupConnectionHandlers();\n    }\n    /**\n     * Returns the URL of the websocket.\n     * @returns string The URL of the websocket.\n     */\n    endpointURL() {\n        return this._appendParams(this.endPoint, Object.assign({}, this.params, { vsn: _lib_constants__WEBPACK_IMPORTED_MODULE_1__.VSN }));\n    }\n    /**\n     * Disconnects the socket.\n     *\n     * @param code A numeric status code to send on disconnect.\n     * @param reason A custom reason for the disconnect.\n     */\n    disconnect(code, reason) {\n        if (this.isDisconnecting()) {\n            return;\n        }\n        this._setConnectionState('disconnecting', true);\n        if (this.conn) {\n            // Setup fallback timer to prevent hanging in disconnecting state\n            const fallbackTimer = setTimeout(() => {\n                this._setConnectionState('disconnected');\n            }, 100);\n            this.conn.onclose = () => {\n                clearTimeout(fallbackTimer);\n                this._setConnectionState('disconnected');\n            };\n            // Close the WebSocket connection\n            if (code) {\n                this.conn.close(code, reason !== null && reason !== void 0 ? reason : '');\n            }\n            else {\n                this.conn.close();\n            }\n            this._teardownConnection();\n        }\n        else {\n            this._setConnectionState('disconnected');\n        }\n    }\n    /**\n     * Returns all created channels\n     */\n    getChannels() {\n        return this.channels;\n    }\n    /**\n     * Unsubscribes and removes a single channel\n     * @param channel A RealtimeChannel instance\n     */\n    async removeChannel(channel) {\n        const status = await channel.unsubscribe();\n        if (this.channels.length === 0) {\n            this.disconnect();\n        }\n        return status;\n    }\n    /**\n     * Unsubscribes and removes all channels\n     */\n    async removeAllChannels() {\n        const values_1 = await Promise.all(this.channels.map((channel) => channel.unsubscribe()));\n        this.channels = [];\n        this.disconnect();\n        return values_1;\n    }\n    /**\n     * Logs the message.\n     *\n     * For customized logging, `this.logger` can be overridden.\n     */\n    log(kind, msg, data) {\n        this.logger(kind, msg, data);\n    }\n    /**\n     * Returns the current state of the socket.\n     */\n    connectionState() {\n        switch (this.conn && this.conn.readyState) {\n            case _lib_constants__WEBPACK_IMPORTED_MODULE_1__.SOCKET_STATES.connecting:\n                return _lib_constants__WEBPACK_IMPORTED_MODULE_1__.CONNECTION_STATE.Connecting;\n            case _lib_constants__WEBPACK_IMPORTED_MODULE_1__.SOCKET_STATES.open:\n                return _lib_constants__WEBPACK_IMPORTED_MODULE_1__.CONNECTION_STATE.Open;\n            case _lib_constants__WEBPACK_IMPORTED_MODULE_1__.SOCKET_STATES.closing:\n                return _lib_constants__WEBPACK_IMPORTED_MODULE_1__.CONNECTION_STATE.Closing;\n            default:\n                return _lib_constants__WEBPACK_IMPORTED_MODULE_1__.CONNECTION_STATE.Closed;\n        }\n    }\n    /**\n     * Returns `true` is the connection is open.\n     */\n    isConnected() {\n        return this.connectionState() === _lib_constants__WEBPACK_IMPORTED_MODULE_1__.CONNECTION_STATE.Open;\n    }\n    /**\n     * Returns `true` if the connection is currently connecting.\n     */\n    isConnecting() {\n        return this._connectionState === 'connecting';\n    }\n    /**\n     * Returns `true` if the connection is currently disconnecting.\n     */\n    isDisconnecting() {\n        return this._connectionState === 'disconnecting';\n    }\n    channel(topic, params = { config: {} }) {\n        const realtimeTopic = `realtime:${topic}`;\n        const exists = this.getChannels().find((c) => c.topic === realtimeTopic);\n        if (!exists) {\n            const chan = new _RealtimeChannel__WEBPACK_IMPORTED_MODULE_5__[\"default\"](`realtime:${topic}`, params, this);\n            this.channels.push(chan);\n            return chan;\n        }\n        else {\n            return exists;\n        }\n    }\n    /**\n     * Push out a message if the socket is connected.\n     *\n     * If the socket is not connected, the message gets enqueued within a local buffer, and sent out when a connection is next established.\n     */\n    push(data) {\n        const { topic, event, payload, ref } = data;\n        const callback = () => {\n            this.encode(data, (result) => {\n                var _a;\n                (_a = this.conn) === null || _a === void 0 ? void 0 : _a.send(result);\n            });\n        };\n        this.log('push', `${topic} ${event} (${ref})`, payload);\n        if (this.isConnected()) {\n            callback();\n        }\n        else {\n            this.sendBuffer.push(callback);\n        }\n    }\n    /**\n     * Sets the JWT access token used for channel subscription authorization and Realtime RLS.\n     *\n     * If param is null it will use the `accessToken` callback function or the token set on the client.\n     *\n     * On callback used, it will set the value of the token internal to the client.\n     *\n     * @param token A JWT string to override the token set on the client.\n     */\n    async setAuth(token = null) {\n        this._authPromise = this._performAuth(token);\n        try {\n            await this._authPromise;\n        }\n        finally {\n            this._authPromise = null;\n        }\n    }\n    /**\n     * Sends a heartbeat message if the socket is connected.\n     */\n    async sendHeartbeat() {\n        var _a;\n        if (!this.isConnected()) {\n            this.heartbeatCallback('disconnected');\n            return;\n        }\n        // Handle heartbeat timeout and force reconnection if needed\n        if (this.pendingHeartbeatRef) {\n            this.pendingHeartbeatRef = null;\n            this.log('transport', 'heartbeat timeout. Attempting to re-establish connection');\n            this.heartbeatCallback('timeout');\n            // Force reconnection after heartbeat timeout\n            this._wasManualDisconnect = false;\n            (_a = this.conn) === null || _a === void 0 ? void 0 : _a.close(_lib_constants__WEBPACK_IMPORTED_MODULE_1__.WS_CLOSE_NORMAL, 'heartbeat timeout');\n            setTimeout(() => {\n                var _a;\n                if (!this.isConnected()) {\n                    (_a = this.reconnectTimer) === null || _a === void 0 ? void 0 : _a.scheduleTimeout();\n                }\n            }, CONNECTION_TIMEOUTS.HEARTBEAT_TIMEOUT_FALLBACK);\n            return;\n        }\n        // Send heartbeat message to server\n        this.pendingHeartbeatRef = this._makeRef();\n        this.push({\n            topic: 'phoenix',\n            event: 'heartbeat',\n            payload: {},\n            ref: this.pendingHeartbeatRef,\n        });\n        this.heartbeatCallback('sent');\n        this._setAuthSafely('heartbeat');\n    }\n    onHeartbeat(callback) {\n        this.heartbeatCallback = callback;\n    }\n    /**\n     * Flushes send buffer\n     */\n    flushSendBuffer() {\n        if (this.isConnected() && this.sendBuffer.length > 0) {\n            this.sendBuffer.forEach((callback) => callback());\n            this.sendBuffer = [];\n        }\n    }\n    /**\n     * Return the next message ref, accounting for overflows\n     *\n     * @internal\n     */\n    _makeRef() {\n        let newRef = this.ref + 1;\n        if (newRef === this.ref) {\n            this.ref = 0;\n        }\n        else {\n            this.ref = newRef;\n        }\n        return this.ref.toString();\n    }\n    /**\n     * Unsubscribe from channels with the specified topic.\n     *\n     * @internal\n     */\n    _leaveOpenTopic(topic) {\n        let dupChannel = this.channels.find((c) => c.topic === topic && (c._isJoined() || c._isJoining()));\n        if (dupChannel) {\n            this.log('transport', `leaving duplicate topic \"${topic}\"`);\n            dupChannel.unsubscribe();\n        }\n    }\n    /**\n     * Removes a subscription from the socket.\n     *\n     * @param channel An open subscription.\n     *\n     * @internal\n     */\n    _remove(channel) {\n        this.channels = this.channels.filter((c) => c.topic !== channel.topic);\n    }\n    /** @internal */\n    _onConnMessage(rawMessage) {\n        this.decode(rawMessage.data, (msg) => {\n            // Handle heartbeat responses\n            if (msg.topic === 'phoenix' && msg.event === 'phx_reply') {\n                this.heartbeatCallback(msg.payload.status === 'ok' ? 'ok' : 'error');\n            }\n            // Handle pending heartbeat reference cleanup\n            if (msg.ref && msg.ref === this.pendingHeartbeatRef) {\n                this.pendingHeartbeatRef = null;\n            }\n            // Log incoming message\n            const { topic, event, payload, ref } = msg;\n            const refString = ref ? `(${ref})` : '';\n            const status = payload.status || '';\n            this.log('receive', `${status} ${topic} ${event} ${refString}`.trim(), payload);\n            // Route message to appropriate channels\n            this.channels\n                .filter((channel) => channel._isMember(topic))\n                .forEach((channel) => channel._trigger(event, payload, ref));\n            this._triggerStateCallbacks('message', msg);\n        });\n    }\n    /**\n     * Clear specific timer\n     * @internal\n     */\n    _clearTimer(timer) {\n        var _a;\n        if (timer === 'heartbeat' && this.heartbeatTimer) {\n            clearInterval(this.heartbeatTimer);\n            this.heartbeatTimer = undefined;\n        }\n        else if (timer === 'reconnect') {\n            (_a = this.reconnectTimer) === null || _a === void 0 ? void 0 : _a.reset();\n        }\n    }\n    /**\n     * Clear all timers\n     * @internal\n     */\n    _clearAllTimers() {\n        this._clearTimer('heartbeat');\n        this._clearTimer('reconnect');\n    }\n    /**\n     * Setup connection handlers for WebSocket events\n     * @internal\n     */\n    _setupConnectionHandlers() {\n        if (!this.conn)\n            return;\n        // Set binary type if supported (browsers and most WebSocket implementations)\n        if ('binaryType' in this.conn) {\n            ;\n            this.conn.binaryType = 'arraybuffer';\n        }\n        this.conn.onopen = () => this._onConnOpen();\n        this.conn.onerror = (error) => this._onConnError(error);\n        this.conn.onmessage = (event) => this._onConnMessage(event);\n        this.conn.onclose = (event) => this._onConnClose(event);\n    }\n    /**\n     * Teardown connection and cleanup resources\n     * @internal\n     */\n    _teardownConnection() {\n        if (this.conn) {\n            this.conn.onopen = null;\n            this.conn.onerror = null;\n            this.conn.onmessage = null;\n            this.conn.onclose = null;\n            this.conn = null;\n        }\n        this._clearAllTimers();\n        this.channels.forEach((channel) => channel.teardown());\n    }\n    /** @internal */\n    _onConnOpen() {\n        this._setConnectionState('connected');\n        this.log('transport', `connected to ${this.endpointURL()}`);\n        this.flushSendBuffer();\n        this._clearTimer('reconnect');\n        if (!this.worker) {\n            this._startHeartbeat();\n        }\n        else {\n            if (!this.workerRef) {\n                this._startWorkerHeartbeat();\n            }\n        }\n        this._triggerStateCallbacks('open');\n    }\n    /** @internal */\n    _startHeartbeat() {\n        this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n        this.heartbeatTimer = setInterval(() => this.sendHeartbeat(), this.heartbeatIntervalMs);\n    }\n    /** @internal */\n    _startWorkerHeartbeat() {\n        if (this.workerUrl) {\n            this.log('worker', `starting worker for from ${this.workerUrl}`);\n        }\n        else {\n            this.log('worker', `starting default worker`);\n        }\n        const objectUrl = this._workerObjectUrl(this.workerUrl);\n        this.workerRef = new Worker(objectUrl);\n        this.workerRef.onerror = (error) => {\n            this.log('worker', 'worker error', error.message);\n            this.workerRef.terminate();\n        };\n        this.workerRef.onmessage = (event) => {\n            if (event.data.event === 'keepAlive') {\n                this.sendHeartbeat();\n            }\n        };\n        this.workerRef.postMessage({\n            event: 'start',\n            interval: this.heartbeatIntervalMs,\n        });\n    }\n    /** @internal */\n    _onConnClose(event) {\n        var _a;\n        this._setConnectionState('disconnected');\n        this.log('transport', 'close', event);\n        this._triggerChanError();\n        this._clearTimer('heartbeat');\n        // Only schedule reconnection if it wasn't a manual disconnect\n        if (!this._wasManualDisconnect) {\n            (_a = this.reconnectTimer) === null || _a === void 0 ? void 0 : _a.scheduleTimeout();\n        }\n        this._triggerStateCallbacks('close', event);\n    }\n    /** @internal */\n    _onConnError(error) {\n        this._setConnectionState('disconnected');\n        this.log('transport', `${error}`);\n        this._triggerChanError();\n        this._triggerStateCallbacks('error', error);\n    }\n    /** @internal */\n    _triggerChanError() {\n        this.channels.forEach((channel) => channel._trigger(_lib_constants__WEBPACK_IMPORTED_MODULE_1__.CHANNEL_EVENTS.error));\n    }\n    /** @internal */\n    _appendParams(url, params) {\n        if (Object.keys(params).length === 0) {\n            return url;\n        }\n        const prefix = url.match(/\\?/) ? '&' : '?';\n        const query = new URLSearchParams(params);\n        return `${url}${prefix}${query}`;\n    }\n    _workerObjectUrl(url) {\n        let result_url;\n        if (url) {\n            result_url = url;\n        }\n        else {\n            const blob = new Blob([WORKER_SCRIPT], { type: 'application/javascript' });\n            result_url = URL.createObjectURL(blob);\n        }\n        return result_url;\n    }\n    /**\n     * Set connection state with proper state management\n     * @internal\n     */\n    _setConnectionState(state, manual = false) {\n        this._connectionState = state;\n        if (state === 'connecting') {\n            this._wasManualDisconnect = false;\n        }\n        else if (state === 'disconnecting') {\n            this._wasManualDisconnect = manual;\n        }\n    }\n    /**\n     * Perform the actual auth operation\n     * @internal\n     */\n    async _performAuth(token = null) {\n        let tokenToSend;\n        if (token) {\n            tokenToSend = token;\n        }\n        else if (this.accessToken) {\n            // Always call the accessToken callback to get fresh token\n            tokenToSend = await this.accessToken();\n        }\n        else {\n            tokenToSend = this.accessTokenValue;\n        }\n        if (this.accessTokenValue != tokenToSend) {\n            this.accessTokenValue = tokenToSend;\n            this.channels.forEach((channel) => {\n                const payload = {\n                    access_token: tokenToSend,\n                    version: _lib_constants__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_VERSION,\n                };\n                tokenToSend && channel.updateJoinPayload(payload);\n                if (channel.joinedOnce && channel._isJoined()) {\n                    channel._push(_lib_constants__WEBPACK_IMPORTED_MODULE_1__.CHANNEL_EVENTS.access_token, {\n                        access_token: tokenToSend,\n                    });\n                }\n            });\n        }\n    }\n    /**\n     * Wait for any in-flight auth operations to complete\n     * @internal\n     */\n    async _waitForAuthIfNeeded() {\n        if (this._authPromise) {\n            await this._authPromise;\n        }\n    }\n    /**\n     * Safely call setAuth with standardized error handling\n     * @internal\n     */\n    _setAuthSafely(context = 'general') {\n        this.setAuth().catch((e) => {\n            this.log('error', `error setting auth in ${context}`, e);\n        });\n    }\n    /**\n     * Trigger state change callbacks with proper error handling\n     * @internal\n     */\n    _triggerStateCallbacks(event, data) {\n        try {\n            this.stateChangeCallbacks[event].forEach((callback) => {\n                try {\n                    callback(data);\n                }\n                catch (e) {\n                    this.log('error', `error in ${event} callback`, e);\n                }\n            });\n        }\n        catch (e) {\n            this.log('error', `error triggering ${event} callbacks`, e);\n        }\n    }\n    /**\n     * Setup reconnection timer with proper configuration\n     * @internal\n     */\n    _setupReconnectionTimer() {\n        this.reconnectTimer = new _lib_timer__WEBPACK_IMPORTED_MODULE_3__[\"default\"](async () => {\n            setTimeout(async () => {\n                await this._waitForAuthIfNeeded();\n                if (!this.isConnected()) {\n                    this.connect();\n                }\n            }, CONNECTION_TIMEOUTS.RECONNECT_DELAY);\n        }, this.reconnectAfterMs);\n    }\n    /**\n     * Initialize client options with defaults\n     * @internal\n     */\n    _initializeOptions(options) {\n        var _a, _b, _c, _d, _e, _f, _g, _h;\n        // Set defaults\n        this.transport = (_a = options === null || options === void 0 ? void 0 : options.transport) !== null && _a !== void 0 ? _a : null;\n        this.timeout = (_b = options === null || options === void 0 ? void 0 : options.timeout) !== null && _b !== void 0 ? _b : _lib_constants__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_TIMEOUT;\n        this.heartbeatIntervalMs =\n            (_c = options === null || options === void 0 ? void 0 : options.heartbeatIntervalMs) !== null && _c !== void 0 ? _c : CONNECTION_TIMEOUTS.HEARTBEAT_INTERVAL;\n        this.worker = (_d = options === null || options === void 0 ? void 0 : options.worker) !== null && _d !== void 0 ? _d : false;\n        this.accessToken = (_e = options === null || options === void 0 ? void 0 : options.accessToken) !== null && _e !== void 0 ? _e : null;\n        // Handle special cases\n        if (options === null || options === void 0 ? void 0 : options.params)\n            this.params = options.params;\n        if (options === null || options === void 0 ? void 0 : options.logger)\n            this.logger = options.logger;\n        if ((options === null || options === void 0 ? void 0 : options.logLevel) || (options === null || options === void 0 ? void 0 : options.log_level)) {\n            this.logLevel = options.logLevel || options.log_level;\n            this.params = Object.assign(Object.assign({}, this.params), { log_level: this.logLevel });\n        }\n        // Set up functions with defaults\n        this.reconnectAfterMs =\n            (_f = options === null || options === void 0 ? void 0 : options.reconnectAfterMs) !== null && _f !== void 0 ? _f : ((tries) => {\n                return RECONNECT_INTERVALS[tries - 1] || DEFAULT_RECONNECT_FALLBACK;\n            });\n        this.encode =\n            (_g = options === null || options === void 0 ? void 0 : options.encode) !== null && _g !== void 0 ? _g : ((payload, callback) => {\n                return callback(JSON.stringify(payload));\n            });\n        this.decode =\n            (_h = options === null || options === void 0 ? void 0 : options.decode) !== null && _h !== void 0 ? _h : this.serializer.decode.bind(this.serializer);\n        // Handle worker setup\n        if (this.worker) {\n            if (typeof window !== 'undefined' && !window.Worker) {\n                throw new Error('Web Worker is not supported');\n            }\n            this.workerUrl = options === null || options === void 0 ? void 0 : options.workerUrl;\n        }\n    }\n}\n//# sourceMappingURL=RealtimeClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   REALTIME_PRESENCE_LISTEN_EVENTS: () => (/* binding */ REALTIME_PRESENCE_LISTEN_EVENTS),\n/* harmony export */   \"default\": () => (/* binding */ RealtimePresence)\n/* harmony export */ });\n/*\n  This file draws heavily from https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/assets/js/phoenix/presence.js\n  License: https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/LICENSE.md\n*/\nvar REALTIME_PRESENCE_LISTEN_EVENTS;\n(function (REALTIME_PRESENCE_LISTEN_EVENTS) {\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"SYNC\"] = \"sync\";\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"JOIN\"] = \"join\";\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"LEAVE\"] = \"leave\";\n})(REALTIME_PRESENCE_LISTEN_EVENTS || (REALTIME_PRESENCE_LISTEN_EVENTS = {}));\nclass RealtimePresence {\n    /**\n     * Initializes the Presence.\n     *\n     * @param channel - The RealtimeChannel\n     * @param opts - The options,\n     *        for example `{events: {state: 'state', diff: 'diff'}}`\n     */\n    constructor(channel, opts) {\n        this.channel = channel;\n        this.state = {};\n        this.pendingDiffs = [];\n        this.joinRef = null;\n        this.enabled = false;\n        this.caller = {\n            onJoin: () => { },\n            onLeave: () => { },\n            onSync: () => { },\n        };\n        const events = (opts === null || opts === void 0 ? void 0 : opts.events) || {\n            state: 'presence_state',\n            diff: 'presence_diff',\n        };\n        this.channel._on(events.state, {}, (newState) => {\n            const { onJoin, onLeave, onSync } = this.caller;\n            this.joinRef = this.channel._joinRef();\n            this.state = RealtimePresence.syncState(this.state, newState, onJoin, onLeave);\n            this.pendingDiffs.forEach((diff) => {\n                this.state = RealtimePresence.syncDiff(this.state, diff, onJoin, onLeave);\n            });\n            this.pendingDiffs = [];\n            onSync();\n        });\n        this.channel._on(events.diff, {}, (diff) => {\n            const { onJoin, onLeave, onSync } = this.caller;\n            if (this.inPendingSyncState()) {\n                this.pendingDiffs.push(diff);\n            }\n            else {\n                this.state = RealtimePresence.syncDiff(this.state, diff, onJoin, onLeave);\n                onSync();\n            }\n        });\n        this.onJoin((key, currentPresences, newPresences) => {\n            this.channel._trigger('presence', {\n                event: 'join',\n                key,\n                currentPresences,\n                newPresences,\n            });\n        });\n        this.onLeave((key, currentPresences, leftPresences) => {\n            this.channel._trigger('presence', {\n                event: 'leave',\n                key,\n                currentPresences,\n                leftPresences,\n            });\n        });\n        this.onSync(() => {\n            this.channel._trigger('presence', { event: 'sync' });\n        });\n    }\n    /**\n     * Used to sync the list of presences on the server with the\n     * client's state.\n     *\n     * An optional `onJoin` and `onLeave` callback can be provided to\n     * react to changes in the client's local presences across\n     * disconnects and reconnects with the server.\n     *\n     * @internal\n     */\n    static syncState(currentState, newState, onJoin, onLeave) {\n        const state = this.cloneDeep(currentState);\n        const transformedState = this.transformState(newState);\n        const joins = {};\n        const leaves = {};\n        this.map(state, (key, presences) => {\n            if (!transformedState[key]) {\n                leaves[key] = presences;\n            }\n        });\n        this.map(transformedState, (key, newPresences) => {\n            const currentPresences = state[key];\n            if (currentPresences) {\n                const newPresenceRefs = newPresences.map((m) => m.presence_ref);\n                const curPresenceRefs = currentPresences.map((m) => m.presence_ref);\n                const joinedPresences = newPresences.filter((m) => curPresenceRefs.indexOf(m.presence_ref) < 0);\n                const leftPresences = currentPresences.filter((m) => newPresenceRefs.indexOf(m.presence_ref) < 0);\n                if (joinedPresences.length > 0) {\n                    joins[key] = joinedPresences;\n                }\n                if (leftPresences.length > 0) {\n                    leaves[key] = leftPresences;\n                }\n            }\n            else {\n                joins[key] = newPresences;\n            }\n        });\n        return this.syncDiff(state, { joins, leaves }, onJoin, onLeave);\n    }\n    /**\n     * Used to sync a diff of presence join and leave events from the\n     * server, as they happen.\n     *\n     * Like `syncState`, `syncDiff` accepts optional `onJoin` and\n     * `onLeave` callbacks to react to a user joining or leaving from a\n     * device.\n     *\n     * @internal\n     */\n    static syncDiff(state, diff, onJoin, onLeave) {\n        const { joins, leaves } = {\n            joins: this.transformState(diff.joins),\n            leaves: this.transformState(diff.leaves),\n        };\n        if (!onJoin) {\n            onJoin = () => { };\n        }\n        if (!onLeave) {\n            onLeave = () => { };\n        }\n        this.map(joins, (key, newPresences) => {\n            var _a;\n            const currentPresences = (_a = state[key]) !== null && _a !== void 0 ? _a : [];\n            state[key] = this.cloneDeep(newPresences);\n            if (currentPresences.length > 0) {\n                const joinedPresenceRefs = state[key].map((m) => m.presence_ref);\n                const curPresences = currentPresences.filter((m) => joinedPresenceRefs.indexOf(m.presence_ref) < 0);\n                state[key].unshift(...curPresences);\n            }\n            onJoin(key, currentPresences, newPresences);\n        });\n        this.map(leaves, (key, leftPresences) => {\n            let currentPresences = state[key];\n            if (!currentPresences)\n                return;\n            const presenceRefsToRemove = leftPresences.map((m) => m.presence_ref);\n            currentPresences = currentPresences.filter((m) => presenceRefsToRemove.indexOf(m.presence_ref) < 0);\n            state[key] = currentPresences;\n            onLeave(key, currentPresences, leftPresences);\n            if (currentPresences.length === 0)\n                delete state[key];\n        });\n        return state;\n    }\n    /** @internal */\n    static map(obj, func) {\n        return Object.getOwnPropertyNames(obj).map((key) => func(key, obj[key]));\n    }\n    /**\n     * Remove 'metas' key\n     * Change 'phx_ref' to 'presence_ref'\n     * Remove 'phx_ref' and 'phx_ref_prev'\n     *\n     * @example\n     * // returns {\n     *  abc123: [\n     *    { presence_ref: '2', user_id: 1 },\n     *    { presence_ref: '3', user_id: 2 }\n     *  ]\n     * }\n     * RealtimePresence.transformState({\n     *  abc123: {\n     *    metas: [\n     *      { phx_ref: '2', phx_ref_prev: '1' user_id: 1 },\n     *      { phx_ref: '3', user_id: 2 }\n     *    ]\n     *  }\n     * })\n     *\n     * @internal\n     */\n    static transformState(state) {\n        state = this.cloneDeep(state);\n        return Object.getOwnPropertyNames(state).reduce((newState, key) => {\n            const presences = state[key];\n            if ('metas' in presences) {\n                newState[key] = presences.metas.map((presence) => {\n                    presence['presence_ref'] = presence['phx_ref'];\n                    delete presence['phx_ref'];\n                    delete presence['phx_ref_prev'];\n                    return presence;\n                });\n            }\n            else {\n                newState[key] = presences;\n            }\n            return newState;\n        }, {});\n    }\n    /** @internal */\n    static cloneDeep(obj) {\n        return JSON.parse(JSON.stringify(obj));\n    }\n    /** @internal */\n    onJoin(callback) {\n        this.caller.onJoin = callback;\n    }\n    /** @internal */\n    onLeave(callback) {\n        this.caller.onLeave = callback;\n    }\n    /** @internal */\n    onSync(callback) {\n        this.caller.onSync = callback;\n    }\n    /** @internal */\n    inPendingSyncState() {\n        return !this.joinRef || this.joinRef !== this.channel._joinRef();\n    }\n}\n//# sourceMappingURL=RealtimePresence.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/index.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/index.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   REALTIME_CHANNEL_STATES: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__.REALTIME_CHANNEL_STATES),\n/* harmony export */   REALTIME_LISTEN_TYPES: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__.REALTIME_LISTEN_TYPES),\n/* harmony export */   REALTIME_POSTGRES_CHANGES_LISTEN_EVENT: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__.REALTIME_POSTGRES_CHANGES_LISTEN_EVENT),\n/* harmony export */   REALTIME_PRESENCE_LISTEN_EVENTS: () => (/* reexport safe */ _RealtimePresence__WEBPACK_IMPORTED_MODULE_2__.REALTIME_PRESENCE_LISTEN_EVENTS),\n/* harmony export */   REALTIME_SUBSCRIBE_STATES: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__.REALTIME_SUBSCRIBE_STATES),\n/* harmony export */   RealtimeChannel: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   RealtimeClient: () => (/* reexport safe */ _RealtimeClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   RealtimePresence: () => (/* reexport safe */ _RealtimePresence__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   WebSocketFactory: () => (/* reexport safe */ _lib_websocket_factory__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _RealtimeClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./RealtimeClient */ \"(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js\");\n/* harmony import */ var _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./RealtimeChannel */ \"(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js\");\n/* harmony import */ var _RealtimePresence__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./RealtimePresence */ \"(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js\");\n/* harmony import */ var _lib_websocket_factory__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/websocket-factory */ \"(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/websocket-factory.js\");\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9Ac3VwYWJhc2UrcmVhbHRpbWUtanNAMi4xNS4wL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvcmVhbHRpbWUtanMvZGlzdC9tb2R1bGUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUE4QztBQUMwSDtBQUNoRjtBQUNqQztBQUM2SztBQUNwTyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxrb21hbFxcRG9jdW1lbnRzXFxqc20tYXBwc1xcbmV4dFxcZWNvbVxcdjAtZG93bmxvYWQtYXR0ZW1wdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK3JlYWx0aW1lLWpzQDIuMTUuMFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHJlYWx0aW1lLWpzXFxkaXN0XFxtb2R1bGVcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFsdGltZUNsaWVudCBmcm9tICcuL1JlYWx0aW1lQ2xpZW50JztcbmltcG9ydCBSZWFsdGltZUNoYW5uZWwsIHsgUkVBTFRJTUVfTElTVEVOX1RZUEVTLCBSRUFMVElNRV9QT1NUR1JFU19DSEFOR0VTX0xJU1RFTl9FVkVOVCwgUkVBTFRJTUVfU1VCU0NSSUJFX1NUQVRFUywgUkVBTFRJTUVfQ0hBTk5FTF9TVEFURVMsIH0gZnJvbSAnLi9SZWFsdGltZUNoYW5uZWwnO1xuaW1wb3J0IFJlYWx0aW1lUHJlc2VuY2UsIHsgUkVBTFRJTUVfUFJFU0VOQ0VfTElTVEVOX0VWRU5UUywgfSBmcm9tICcuL1JlYWx0aW1lUHJlc2VuY2UnO1xuaW1wb3J0IFdlYlNvY2tldEZhY3RvcnkgZnJvbSAnLi9saWIvd2Vic29ja2V0LWZhY3RvcnknO1xuZXhwb3J0IHsgUmVhbHRpbWVQcmVzZW5jZSwgUmVhbHRpbWVDaGFubmVsLCBSZWFsdGltZUNsaWVudCwgUkVBTFRJTUVfTElTVEVOX1RZUEVTLCBSRUFMVElNRV9QT1NUR1JFU19DSEFOR0VTX0xJU1RFTl9FVkVOVCwgUkVBTFRJTUVfUFJFU0VOQ0VfTElTVEVOX0VWRU5UUywgUkVBTFRJTUVfU1VCU0NSSUJFX1NUQVRFUywgUkVBTFRJTUVfQ0hBTk5FTF9TVEFURVMsIFdlYlNvY2tldEZhY3RvcnksIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/constants.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/constants.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CHANNEL_EVENTS: () => (/* binding */ CHANNEL_EVENTS),\n/* harmony export */   CHANNEL_STATES: () => (/* binding */ CHANNEL_STATES),\n/* harmony export */   CONNECTION_STATE: () => (/* binding */ CONNECTION_STATE),\n/* harmony export */   DEFAULT_TIMEOUT: () => (/* binding */ DEFAULT_TIMEOUT),\n/* harmony export */   DEFAULT_VERSION: () => (/* binding */ DEFAULT_VERSION),\n/* harmony export */   MAX_PUSH_BUFFER_SIZE: () => (/* binding */ MAX_PUSH_BUFFER_SIZE),\n/* harmony export */   SOCKET_STATES: () => (/* binding */ SOCKET_STATES),\n/* harmony export */   TRANSPORTS: () => (/* binding */ TRANSPORTS),\n/* harmony export */   VERSION: () => (/* binding */ VERSION),\n/* harmony export */   VSN: () => (/* binding */ VSN),\n/* harmony export */   WS_CLOSE_NORMAL: () => (/* binding */ WS_CLOSE_NORMAL)\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/version.js\");\n\nconst DEFAULT_VERSION = `realtime-js/${_version__WEBPACK_IMPORTED_MODULE_0__.version}`;\nconst VSN = '1.0.0';\nconst VERSION = _version__WEBPACK_IMPORTED_MODULE_0__.version;\nconst DEFAULT_TIMEOUT = 10000;\nconst WS_CLOSE_NORMAL = 1000;\nconst MAX_PUSH_BUFFER_SIZE = 100;\nvar SOCKET_STATES;\n(function (SOCKET_STATES) {\n    SOCKET_STATES[SOCKET_STATES[\"connecting\"] = 0] = \"connecting\";\n    SOCKET_STATES[SOCKET_STATES[\"open\"] = 1] = \"open\";\n    SOCKET_STATES[SOCKET_STATES[\"closing\"] = 2] = \"closing\";\n    SOCKET_STATES[SOCKET_STATES[\"closed\"] = 3] = \"closed\";\n})(SOCKET_STATES || (SOCKET_STATES = {}));\nvar CHANNEL_STATES;\n(function (CHANNEL_STATES) {\n    CHANNEL_STATES[\"closed\"] = \"closed\";\n    CHANNEL_STATES[\"errored\"] = \"errored\";\n    CHANNEL_STATES[\"joined\"] = \"joined\";\n    CHANNEL_STATES[\"joining\"] = \"joining\";\n    CHANNEL_STATES[\"leaving\"] = \"leaving\";\n})(CHANNEL_STATES || (CHANNEL_STATES = {}));\nvar CHANNEL_EVENTS;\n(function (CHANNEL_EVENTS) {\n    CHANNEL_EVENTS[\"close\"] = \"phx_close\";\n    CHANNEL_EVENTS[\"error\"] = \"phx_error\";\n    CHANNEL_EVENTS[\"join\"] = \"phx_join\";\n    CHANNEL_EVENTS[\"reply\"] = \"phx_reply\";\n    CHANNEL_EVENTS[\"leave\"] = \"phx_leave\";\n    CHANNEL_EVENTS[\"access_token\"] = \"access_token\";\n})(CHANNEL_EVENTS || (CHANNEL_EVENTS = {}));\nvar TRANSPORTS;\n(function (TRANSPORTS) {\n    TRANSPORTS[\"websocket\"] = \"websocket\";\n})(TRANSPORTS || (TRANSPORTS = {}));\nvar CONNECTION_STATE;\n(function (CONNECTION_STATE) {\n    CONNECTION_STATE[\"Connecting\"] = \"connecting\";\n    CONNECTION_STATE[\"Open\"] = \"open\";\n    CONNECTION_STATE[\"Closing\"] = \"closing\";\n    CONNECTION_STATE[\"Closed\"] = \"closed\";\n})(CONNECTION_STATE || (CONNECTION_STATE = {}));\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/constants.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/push.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/push.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Push)\n/* harmony export */ });\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/constants */ \"(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/constants.js\");\n\nclass Push {\n    /**\n     * Initializes the Push\n     *\n     * @param channel The Channel\n     * @param event The event, for example `\"phx_join\"`\n     * @param payload The payload, for example `{user_id: 123}`\n     * @param timeout The push timeout in milliseconds\n     */\n    constructor(channel, event, payload = {}, timeout = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_TIMEOUT) {\n        this.channel = channel;\n        this.event = event;\n        this.payload = payload;\n        this.timeout = timeout;\n        this.sent = false;\n        this.timeoutTimer = undefined;\n        this.ref = '';\n        this.receivedResp = null;\n        this.recHooks = [];\n        this.refEvent = null;\n    }\n    resend(timeout) {\n        this.timeout = timeout;\n        this._cancelRefEvent();\n        this.ref = '';\n        this.refEvent = null;\n        this.receivedResp = null;\n        this.sent = false;\n        this.send();\n    }\n    send() {\n        if (this._hasReceived('timeout')) {\n            return;\n        }\n        this.startTimeout();\n        this.sent = true;\n        this.channel.socket.push({\n            topic: this.channel.topic,\n            event: this.event,\n            payload: this.payload,\n            ref: this.ref,\n            join_ref: this.channel._joinRef(),\n        });\n    }\n    updatePayload(payload) {\n        this.payload = Object.assign(Object.assign({}, this.payload), payload);\n    }\n    receive(status, callback) {\n        var _a;\n        if (this._hasReceived(status)) {\n            callback((_a = this.receivedResp) === null || _a === void 0 ? void 0 : _a.response);\n        }\n        this.recHooks.push({ status, callback });\n        return this;\n    }\n    startTimeout() {\n        if (this.timeoutTimer) {\n            return;\n        }\n        this.ref = this.channel.socket._makeRef();\n        this.refEvent = this.channel._replyEventName(this.ref);\n        const callback = (payload) => {\n            this._cancelRefEvent();\n            this._cancelTimeout();\n            this.receivedResp = payload;\n            this._matchReceive(payload);\n        };\n        this.channel._on(this.refEvent, {}, callback);\n        this.timeoutTimer = setTimeout(() => {\n            this.trigger('timeout', {});\n        }, this.timeout);\n    }\n    trigger(status, response) {\n        if (this.refEvent)\n            this.channel._trigger(this.refEvent, { status, response });\n    }\n    destroy() {\n        this._cancelRefEvent();\n        this._cancelTimeout();\n    }\n    _cancelRefEvent() {\n        if (!this.refEvent) {\n            return;\n        }\n        this.channel._off(this.refEvent, {});\n    }\n    _cancelTimeout() {\n        clearTimeout(this.timeoutTimer);\n        this.timeoutTimer = undefined;\n    }\n    _matchReceive({ status, response, }) {\n        this.recHooks\n            .filter((h) => h.status === status)\n            .forEach((h) => h.callback(response));\n    }\n    _hasReceived(status) {\n        return this.receivedResp && this.receivedResp.status === status;\n    }\n}\n//# sourceMappingURL=push.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/push.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/serializer.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/serializer.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Serializer)\n/* harmony export */ });\n// This file draws heavily from https://github.com/phoenixframework/phoenix/commit/cf098e9cf7a44ee6479d31d911a97d3c7430c6fe\n// License: https://github.com/phoenixframework/phoenix/blob/master/LICENSE.md\nclass Serializer {\n    constructor() {\n        this.HEADER_LENGTH = 1;\n    }\n    decode(rawPayload, callback) {\n        if (rawPayload.constructor === ArrayBuffer) {\n            return callback(this._binaryDecode(rawPayload));\n        }\n        if (typeof rawPayload === 'string') {\n            return callback(JSON.parse(rawPayload));\n        }\n        return callback({});\n    }\n    _binaryDecode(buffer) {\n        const view = new DataView(buffer);\n        const decoder = new TextDecoder();\n        return this._decodeBroadcast(buffer, view, decoder);\n    }\n    _decodeBroadcast(buffer, view, decoder) {\n        const topicSize = view.getUint8(1);\n        const eventSize = view.getUint8(2);\n        let offset = this.HEADER_LENGTH + 2;\n        const topic = decoder.decode(buffer.slice(offset, offset + topicSize));\n        offset = offset + topicSize;\n        const event = decoder.decode(buffer.slice(offset, offset + eventSize));\n        offset = offset + eventSize;\n        const data = JSON.parse(decoder.decode(buffer.slice(offset, buffer.byteLength)));\n        return { ref: null, topic: topic, event: event, payload: data };\n    }\n}\n//# sourceMappingURL=serializer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/serializer.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/timer.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/timer.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Timer)\n/* harmony export */ });\n/**\n * Creates a timer that accepts a `timerCalc` function to perform calculated timeout retries, such as exponential backoff.\n *\n * @example\n *    let reconnectTimer = new Timer(() => this.connect(), function(tries){\n *      return [1000, 5000, 10000][tries - 1] || 10000\n *    })\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n *    reconnectTimer.scheduleTimeout() // fires after 5000\n *    reconnectTimer.reset()\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n */\nclass Timer {\n    constructor(callback, timerCalc) {\n        this.callback = callback;\n        this.timerCalc = timerCalc;\n        this.timer = undefined;\n        this.tries = 0;\n        this.callback = callback;\n        this.timerCalc = timerCalc;\n    }\n    reset() {\n        this.tries = 0;\n        clearTimeout(this.timer);\n        this.timer = undefined;\n    }\n    // Cancels any previous scheduleTimeout and schedules callback\n    scheduleTimeout() {\n        clearTimeout(this.timer);\n        this.timer = setTimeout(() => {\n            this.tries = this.tries + 1;\n            this.callback();\n        }, this.timerCalc(this.tries + 1));\n    }\n}\n//# sourceMappingURL=timer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/timer.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostgresTypes: () => (/* binding */ PostgresTypes),\n/* harmony export */   convertCell: () => (/* binding */ convertCell),\n/* harmony export */   convertChangeData: () => (/* binding */ convertChangeData),\n/* harmony export */   convertColumn: () => (/* binding */ convertColumn),\n/* harmony export */   httpEndpointURL: () => (/* binding */ httpEndpointURL),\n/* harmony export */   toArray: () => (/* binding */ toArray),\n/* harmony export */   toBoolean: () => (/* binding */ toBoolean),\n/* harmony export */   toJson: () => (/* binding */ toJson),\n/* harmony export */   toNumber: () => (/* binding */ toNumber),\n/* harmony export */   toTimestampString: () => (/* binding */ toTimestampString)\n/* harmony export */ });\n/**\n * Helpers to convert the change Payload into native JS types.\n */\n// Adapted from epgsql (src/epgsql_binary.erl), this module licensed under\n// 3-clause BSD found here: https://raw.githubusercontent.com/epgsql/epgsql/devel/LICENSE\nvar PostgresTypes;\n(function (PostgresTypes) {\n    PostgresTypes[\"abstime\"] = \"abstime\";\n    PostgresTypes[\"bool\"] = \"bool\";\n    PostgresTypes[\"date\"] = \"date\";\n    PostgresTypes[\"daterange\"] = \"daterange\";\n    PostgresTypes[\"float4\"] = \"float4\";\n    PostgresTypes[\"float8\"] = \"float8\";\n    PostgresTypes[\"int2\"] = \"int2\";\n    PostgresTypes[\"int4\"] = \"int4\";\n    PostgresTypes[\"int4range\"] = \"int4range\";\n    PostgresTypes[\"int8\"] = \"int8\";\n    PostgresTypes[\"int8range\"] = \"int8range\";\n    PostgresTypes[\"json\"] = \"json\";\n    PostgresTypes[\"jsonb\"] = \"jsonb\";\n    PostgresTypes[\"money\"] = \"money\";\n    PostgresTypes[\"numeric\"] = \"numeric\";\n    PostgresTypes[\"oid\"] = \"oid\";\n    PostgresTypes[\"reltime\"] = \"reltime\";\n    PostgresTypes[\"text\"] = \"text\";\n    PostgresTypes[\"time\"] = \"time\";\n    PostgresTypes[\"timestamp\"] = \"timestamp\";\n    PostgresTypes[\"timestamptz\"] = \"timestamptz\";\n    PostgresTypes[\"timetz\"] = \"timetz\";\n    PostgresTypes[\"tsrange\"] = \"tsrange\";\n    PostgresTypes[\"tstzrange\"] = \"tstzrange\";\n})(PostgresTypes || (PostgresTypes = {}));\n/**\n * Takes an array of columns and an object of string values then converts each string value\n * to its mapped type.\n *\n * @param {{name: String, type: String}[]} columns\n * @param {Object} record\n * @param {Object} options The map of various options that can be applied to the mapper\n * @param {Array} options.skipTypes The array of types that should not be converted\n *\n * @example convertChangeData([{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age:'33'}, {})\n * //=>{ first_name: 'Paul', age: 33 }\n */\nconst convertChangeData = (columns, record, options = {}) => {\n    var _a;\n    const skipTypes = (_a = options.skipTypes) !== null && _a !== void 0 ? _a : [];\n    return Object.keys(record).reduce((acc, rec_key) => {\n        acc[rec_key] = convertColumn(rec_key, columns, record, skipTypes);\n        return acc;\n    }, {});\n};\n/**\n * Converts the value of an individual column.\n *\n * @param {String} columnName The column that you want to convert\n * @param {{name: String, type: String}[]} columns All of the columns\n * @param {Object} record The map of string values\n * @param {Array} skipTypes An array of types that should not be converted\n * @return {object} Useless information\n *\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, [])\n * //=> 33\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, ['int4'])\n * //=> \"33\"\n */\nconst convertColumn = (columnName, columns, record, skipTypes) => {\n    const column = columns.find((x) => x.name === columnName);\n    const colType = column === null || column === void 0 ? void 0 : column.type;\n    const value = record[columnName];\n    if (colType && !skipTypes.includes(colType)) {\n        return convertCell(colType, value);\n    }\n    return noop(value);\n};\n/**\n * If the value of the cell is `null`, returns null.\n * Otherwise converts the string value to the correct type.\n * @param {String} type A postgres column type\n * @param {String} value The cell value\n *\n * @example convertCell('bool', 't')\n * //=> true\n * @example convertCell('int8', '10')\n * //=> 10\n * @example convertCell('_int4', '{1,2,3,4}')\n * //=> [1,2,3,4]\n */\nconst convertCell = (type, value) => {\n    // if data type is an array\n    if (type.charAt(0) === '_') {\n        const dataType = type.slice(1, type.length);\n        return toArray(value, dataType);\n    }\n    // If not null, convert to correct type.\n    switch (type) {\n        case PostgresTypes.bool:\n            return toBoolean(value);\n        case PostgresTypes.float4:\n        case PostgresTypes.float8:\n        case PostgresTypes.int2:\n        case PostgresTypes.int4:\n        case PostgresTypes.int8:\n        case PostgresTypes.numeric:\n        case PostgresTypes.oid:\n            return toNumber(value);\n        case PostgresTypes.json:\n        case PostgresTypes.jsonb:\n            return toJson(value);\n        case PostgresTypes.timestamp:\n            return toTimestampString(value); // Format to be consistent with PostgREST\n        case PostgresTypes.abstime: // To allow users to cast it based on Timezone\n        case PostgresTypes.date: // To allow users to cast it based on Timezone\n        case PostgresTypes.daterange:\n        case PostgresTypes.int4range:\n        case PostgresTypes.int8range:\n        case PostgresTypes.money:\n        case PostgresTypes.reltime: // To allow users to cast it based on Timezone\n        case PostgresTypes.text:\n        case PostgresTypes.time: // To allow users to cast it based on Timezone\n        case PostgresTypes.timestamptz: // To allow users to cast it based on Timezone\n        case PostgresTypes.timetz: // To allow users to cast it based on Timezone\n        case PostgresTypes.tsrange:\n        case PostgresTypes.tstzrange:\n            return noop(value);\n        default:\n            // Return the value for remaining types\n            return noop(value);\n    }\n};\nconst noop = (value) => {\n    return value;\n};\nconst toBoolean = (value) => {\n    switch (value) {\n        case 't':\n            return true;\n        case 'f':\n            return false;\n        default:\n            return value;\n    }\n};\nconst toNumber = (value) => {\n    if (typeof value === 'string') {\n        const parsedValue = parseFloat(value);\n        if (!Number.isNaN(parsedValue)) {\n            return parsedValue;\n        }\n    }\n    return value;\n};\nconst toJson = (value) => {\n    if (typeof value === 'string') {\n        try {\n            return JSON.parse(value);\n        }\n        catch (error) {\n            console.log(`JSON parse error: ${error}`);\n            return value;\n        }\n    }\n    return value;\n};\n/**\n * Converts a Postgres Array into a native JS array\n *\n * @example toArray('{}', 'int4')\n * //=> []\n * @example toArray('{\"[2021-01-01,2021-12-31)\",\"(2021-01-01,2021-12-32]\"}', 'daterange')\n * //=> ['[2021-01-01,2021-12-31)', '(2021-01-01,2021-12-32]']\n * @example toArray([1,2,3,4], 'int4')\n * //=> [1,2,3,4]\n */\nconst toArray = (value, type) => {\n    if (typeof value !== 'string') {\n        return value;\n    }\n    const lastIdx = value.length - 1;\n    const closeBrace = value[lastIdx];\n    const openBrace = value[0];\n    // Confirm value is a Postgres array by checking curly brackets\n    if (openBrace === '{' && closeBrace === '}') {\n        let arr;\n        const valTrim = value.slice(1, lastIdx);\n        // TODO: find a better solution to separate Postgres array data\n        try {\n            arr = JSON.parse('[' + valTrim + ']');\n        }\n        catch (_) {\n            // WARNING: splitting on comma does not cover all edge cases\n            arr = valTrim ? valTrim.split(',') : [];\n        }\n        return arr.map((val) => convertCell(type, val));\n    }\n    return value;\n};\n/**\n * Fixes timestamp to be ISO-8601. Swaps the space between the date and time for a 'T'\n * See https://github.com/supabase/supabase/issues/18\n *\n * @example toTimestampString('2019-09-10 00:00:00')\n * //=> '2019-09-10T00:00:00'\n */\nconst toTimestampString = (value) => {\n    if (typeof value === 'string') {\n        return value.replace(' ', 'T');\n    }\n    return value;\n};\nconst httpEndpointURL = (socketUrl) => {\n    let url = socketUrl;\n    url = url.replace(/^ws/i, 'http');\n    url = url.replace(/(\\/socket\\/websocket|\\/socket|\\/websocket)\\/?$/i, '');\n    return url.replace(/\\/+$/, '') + '/api/broadcast';\n};\n//# sourceMappingURL=transformers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9Ac3VwYWJhc2UrcmVhbHRpbWUtanNAMi4xNS4wL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvcmVhbHRpbWUtanMvZGlzdC9tb2R1bGUvbGliL3RyYW5zZm9ybWVycy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLHNDQUFzQztBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksMkJBQTJCLElBQUk7QUFDM0MsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQixXQUFXLE9BQU87QUFDbEI7QUFDQSxnQ0FBZ0MsaUNBQWlDLEdBQUcsMEJBQTBCLElBQUksNkJBQTZCLElBQUk7QUFDbkksU0FBUztBQUNUO0FBQ08sd0RBQXdEO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLLElBQUk7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixZQUFZLDJCQUEyQixJQUFJO0FBQzNDLFdBQVcsUUFBUTtBQUNuQixXQUFXLE9BQU87QUFDbEIsWUFBWSxRQUFRO0FBQ3BCO0FBQ0EsbUNBQW1DLGlDQUFpQyxHQUFHLDBCQUEwQixJQUFJLDhCQUE4QjtBQUNuSTtBQUNBLG1DQUFtQyxpQ0FBaUMsR0FBRywwQkFBMEIsSUFBSSw4QkFBOEI7QUFDbkk7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLFFBQVE7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyxRQUFRO0FBQzNDO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZDQUE2QztBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkNBQTZDLE1BQU07QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QjtBQUN2QjtBQUNBLHNCQUFzQixvREFBb0Q7QUFDMUU7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHNCQUFzQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxrb21hbFxcRG9jdW1lbnRzXFxqc20tYXBwc1xcbmV4dFxcZWNvbVxcdjAtZG93bmxvYWQtYXR0ZW1wdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK3JlYWx0aW1lLWpzQDIuMTUuMFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHJlYWx0aW1lLWpzXFxkaXN0XFxtb2R1bGVcXGxpYlxcdHJhbnNmb3JtZXJzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogSGVscGVycyB0byBjb252ZXJ0IHRoZSBjaGFuZ2UgUGF5bG9hZCBpbnRvIG5hdGl2ZSBKUyB0eXBlcy5cbiAqL1xuLy8gQWRhcHRlZCBmcm9tIGVwZ3NxbCAoc3JjL2VwZ3NxbF9iaW5hcnkuZXJsKSwgdGhpcyBtb2R1bGUgbGljZW5zZWQgdW5kZXJcbi8vIDMtY2xhdXNlIEJTRCBmb3VuZCBoZXJlOiBodHRwczovL3Jhdy5naXRodWJ1c2VyY29udGVudC5jb20vZXBnc3FsL2VwZ3NxbC9kZXZlbC9MSUNFTlNFXG5leHBvcnQgdmFyIFBvc3RncmVzVHlwZXM7XG4oZnVuY3Rpb24gKFBvc3RncmVzVHlwZXMpIHtcbiAgICBQb3N0Z3Jlc1R5cGVzW1wiYWJzdGltZVwiXSA9IFwiYWJzdGltZVwiO1xuICAgIFBvc3RncmVzVHlwZXNbXCJib29sXCJdID0gXCJib29sXCI7XG4gICAgUG9zdGdyZXNUeXBlc1tcImRhdGVcIl0gPSBcImRhdGVcIjtcbiAgICBQb3N0Z3Jlc1R5cGVzW1wiZGF0ZXJhbmdlXCJdID0gXCJkYXRlcmFuZ2VcIjtcbiAgICBQb3N0Z3Jlc1R5cGVzW1wiZmxvYXQ0XCJdID0gXCJmbG9hdDRcIjtcbiAgICBQb3N0Z3Jlc1R5cGVzW1wiZmxvYXQ4XCJdID0gXCJmbG9hdDhcIjtcbiAgICBQb3N0Z3Jlc1R5cGVzW1wiaW50MlwiXSA9IFwiaW50MlwiO1xuICAgIFBvc3RncmVzVHlwZXNbXCJpbnQ0XCJdID0gXCJpbnQ0XCI7XG4gICAgUG9zdGdyZXNUeXBlc1tcImludDRyYW5nZVwiXSA9IFwiaW50NHJhbmdlXCI7XG4gICAgUG9zdGdyZXNUeXBlc1tcImludDhcIl0gPSBcImludDhcIjtcbiAgICBQb3N0Z3Jlc1R5cGVzW1wiaW50OHJhbmdlXCJdID0gXCJpbnQ4cmFuZ2VcIjtcbiAgICBQb3N0Z3Jlc1R5cGVzW1wianNvblwiXSA9IFwianNvblwiO1xuICAgIFBvc3RncmVzVHlwZXNbXCJqc29uYlwiXSA9IFwianNvbmJcIjtcbiAgICBQb3N0Z3Jlc1R5cGVzW1wibW9uZXlcIl0gPSBcIm1vbmV5XCI7XG4gICAgUG9zdGdyZXNUeXBlc1tcIm51bWVyaWNcIl0gPSBcIm51bWVyaWNcIjtcbiAgICBQb3N0Z3Jlc1R5cGVzW1wib2lkXCJdID0gXCJvaWRcIjtcbiAgICBQb3N0Z3Jlc1R5cGVzW1wicmVsdGltZVwiXSA9IFwicmVsdGltZVwiO1xuICAgIFBvc3RncmVzVHlwZXNbXCJ0ZXh0XCJdID0gXCJ0ZXh0XCI7XG4gICAgUG9zdGdyZXNUeXBlc1tcInRpbWVcIl0gPSBcInRpbWVcIjtcbiAgICBQb3N0Z3Jlc1R5cGVzW1widGltZXN0YW1wXCJdID0gXCJ0aW1lc3RhbXBcIjtcbiAgICBQb3N0Z3Jlc1R5cGVzW1widGltZXN0YW1wdHpcIl0gPSBcInRpbWVzdGFtcHR6XCI7XG4gICAgUG9zdGdyZXNUeXBlc1tcInRpbWV0elwiXSA9IFwidGltZXR6XCI7XG4gICAgUG9zdGdyZXNUeXBlc1tcInRzcmFuZ2VcIl0gPSBcInRzcmFuZ2VcIjtcbiAgICBQb3N0Z3Jlc1R5cGVzW1widHN0enJhbmdlXCJdID0gXCJ0c3R6cmFuZ2VcIjtcbn0pKFBvc3RncmVzVHlwZXMgfHwgKFBvc3RncmVzVHlwZXMgPSB7fSkpO1xuLyoqXG4gKiBUYWtlcyBhbiBhcnJheSBvZiBjb2x1bW5zIGFuZCBhbiBvYmplY3Qgb2Ygc3RyaW5nIHZhbHVlcyB0aGVuIGNvbnZlcnRzIGVhY2ggc3RyaW5nIHZhbHVlXG4gKiB0byBpdHMgbWFwcGVkIHR5cGUuXG4gKlxuICogQHBhcmFtIHt7bmFtZTogU3RyaW5nLCB0eXBlOiBTdHJpbmd9W119IGNvbHVtbnNcbiAqIEBwYXJhbSB7T2JqZWN0fSByZWNvcmRcbiAqIEBwYXJhbSB7T2JqZWN0fSBvcHRpb25zIFRoZSBtYXAgb2YgdmFyaW91cyBvcHRpb25zIHRoYXQgY2FuIGJlIGFwcGxpZWQgdG8gdGhlIG1hcHBlclxuICogQHBhcmFtIHtBcnJheX0gb3B0aW9ucy5za2lwVHlwZXMgVGhlIGFycmF5IG9mIHR5cGVzIHRoYXQgc2hvdWxkIG5vdCBiZSBjb252ZXJ0ZWRcbiAqXG4gKiBAZXhhbXBsZSBjb252ZXJ0Q2hhbmdlRGF0YShbe25hbWU6ICdmaXJzdF9uYW1lJywgdHlwZTogJ3RleHQnfSwge25hbWU6ICdhZ2UnLCB0eXBlOiAnaW50NCd9XSwge2ZpcnN0X25hbWU6ICdQYXVsJywgYWdlOiczMyd9LCB7fSlcbiAqIC8vPT57IGZpcnN0X25hbWU6ICdQYXVsJywgYWdlOiAzMyB9XG4gKi9cbmV4cG9ydCBjb25zdCBjb252ZXJ0Q2hhbmdlRGF0YSA9IChjb2x1bW5zLCByZWNvcmQsIG9wdGlvbnMgPSB7fSkgPT4ge1xuICAgIHZhciBfYTtcbiAgICBjb25zdCBza2lwVHlwZXMgPSAoX2EgPSBvcHRpb25zLnNraXBUeXBlcykgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogW107XG4gICAgcmV0dXJuIE9iamVjdC5rZXlzKHJlY29yZCkucmVkdWNlKChhY2MsIHJlY19rZXkpID0+IHtcbiAgICAgICAgYWNjW3JlY19rZXldID0gY29udmVydENvbHVtbihyZWNfa2V5LCBjb2x1bW5zLCByZWNvcmQsIHNraXBUeXBlcyk7XG4gICAgICAgIHJldHVybiBhY2M7XG4gICAgfSwge30pO1xufTtcbi8qKlxuICogQ29udmVydHMgdGhlIHZhbHVlIG9mIGFuIGluZGl2aWR1YWwgY29sdW1uLlxuICpcbiAqIEBwYXJhbSB7U3RyaW5nfSBjb2x1bW5OYW1lIFRoZSBjb2x1bW4gdGhhdCB5b3Ugd2FudCB0byBjb252ZXJ0XG4gKiBAcGFyYW0ge3tuYW1lOiBTdHJpbmcsIHR5cGU6IFN0cmluZ31bXX0gY29sdW1ucyBBbGwgb2YgdGhlIGNvbHVtbnNcbiAqIEBwYXJhbSB7T2JqZWN0fSByZWNvcmQgVGhlIG1hcCBvZiBzdHJpbmcgdmFsdWVzXG4gKiBAcGFyYW0ge0FycmF5fSBza2lwVHlwZXMgQW4gYXJyYXkgb2YgdHlwZXMgdGhhdCBzaG91bGQgbm90IGJlIGNvbnZlcnRlZFxuICogQHJldHVybiB7b2JqZWN0fSBVc2VsZXNzIGluZm9ybWF0aW9uXG4gKlxuICogQGV4YW1wbGUgY29udmVydENvbHVtbignYWdlJywgW3tuYW1lOiAnZmlyc3RfbmFtZScsIHR5cGU6ICd0ZXh0J30sIHtuYW1lOiAnYWdlJywgdHlwZTogJ2ludDQnfV0sIHtmaXJzdF9uYW1lOiAnUGF1bCcsIGFnZTogJzMzJ30sIFtdKVxuICogLy89PiAzM1xuICogQGV4YW1wbGUgY29udmVydENvbHVtbignYWdlJywgW3tuYW1lOiAnZmlyc3RfbmFtZScsIHR5cGU6ICd0ZXh0J30sIHtuYW1lOiAnYWdlJywgdHlwZTogJ2ludDQnfV0sIHtmaXJzdF9uYW1lOiAnUGF1bCcsIGFnZTogJzMzJ30sIFsnaW50NCddKVxuICogLy89PiBcIjMzXCJcbiAqL1xuZXhwb3J0IGNvbnN0IGNvbnZlcnRDb2x1bW4gPSAoY29sdW1uTmFtZSwgY29sdW1ucywgcmVjb3JkLCBza2lwVHlwZXMpID0+IHtcbiAgICBjb25zdCBjb2x1bW4gPSBjb2x1bW5zLmZpbmQoKHgpID0+IHgubmFtZSA9PT0gY29sdW1uTmFtZSk7XG4gICAgY29uc3QgY29sVHlwZSA9IGNvbHVtbiA9PT0gbnVsbCB8fCBjb2x1bW4gPT09IHZvaWQgMCA/IHZvaWQgMCA6IGNvbHVtbi50eXBlO1xuICAgIGNvbnN0IHZhbHVlID0gcmVjb3JkW2NvbHVtbk5hbWVdO1xuICAgIGlmIChjb2xUeXBlICYmICFza2lwVHlwZXMuaW5jbHVkZXMoY29sVHlwZSkpIHtcbiAgICAgICAgcmV0dXJuIGNvbnZlcnRDZWxsKGNvbFR5cGUsIHZhbHVlKTtcbiAgICB9XG4gICAgcmV0dXJuIG5vb3AodmFsdWUpO1xufTtcbi8qKlxuICogSWYgdGhlIHZhbHVlIG9mIHRoZSBjZWxsIGlzIGBudWxsYCwgcmV0dXJucyBudWxsLlxuICogT3RoZXJ3aXNlIGNvbnZlcnRzIHRoZSBzdHJpbmcgdmFsdWUgdG8gdGhlIGNvcnJlY3QgdHlwZS5cbiAqIEBwYXJhbSB7U3RyaW5nfSB0eXBlIEEgcG9zdGdyZXMgY29sdW1uIHR5cGVcbiAqIEBwYXJhbSB7U3RyaW5nfSB2YWx1ZSBUaGUgY2VsbCB2YWx1ZVxuICpcbiAqIEBleGFtcGxlIGNvbnZlcnRDZWxsKCdib29sJywgJ3QnKVxuICogLy89PiB0cnVlXG4gKiBAZXhhbXBsZSBjb252ZXJ0Q2VsbCgnaW50OCcsICcxMCcpXG4gKiAvLz0+IDEwXG4gKiBAZXhhbXBsZSBjb252ZXJ0Q2VsbCgnX2ludDQnLCAnezEsMiwzLDR9JylcbiAqIC8vPT4gWzEsMiwzLDRdXG4gKi9cbmV4cG9ydCBjb25zdCBjb252ZXJ0Q2VsbCA9ICh0eXBlLCB2YWx1ZSkgPT4ge1xuICAgIC8vIGlmIGRhdGEgdHlwZSBpcyBhbiBhcnJheVxuICAgIGlmICh0eXBlLmNoYXJBdCgwKSA9PT0gJ18nKSB7XG4gICAgICAgIGNvbnN0IGRhdGFUeXBlID0gdHlwZS5zbGljZSgxLCB0eXBlLmxlbmd0aCk7XG4gICAgICAgIHJldHVybiB0b0FycmF5KHZhbHVlLCBkYXRhVHlwZSk7XG4gICAgfVxuICAgIC8vIElmIG5vdCBudWxsLCBjb252ZXJ0IHRvIGNvcnJlY3QgdHlwZS5cbiAgICBzd2l0Y2ggKHR5cGUpIHtcbiAgICAgICAgY2FzZSBQb3N0Z3Jlc1R5cGVzLmJvb2w6XG4gICAgICAgICAgICByZXR1cm4gdG9Cb29sZWFuKHZhbHVlKTtcbiAgICAgICAgY2FzZSBQb3N0Z3Jlc1R5cGVzLmZsb2F0NDpcbiAgICAgICAgY2FzZSBQb3N0Z3Jlc1R5cGVzLmZsb2F0ODpcbiAgICAgICAgY2FzZSBQb3N0Z3Jlc1R5cGVzLmludDI6XG4gICAgICAgIGNhc2UgUG9zdGdyZXNUeXBlcy5pbnQ0OlxuICAgICAgICBjYXNlIFBvc3RncmVzVHlwZXMuaW50ODpcbiAgICAgICAgY2FzZSBQb3N0Z3Jlc1R5cGVzLm51bWVyaWM6XG4gICAgICAgIGNhc2UgUG9zdGdyZXNUeXBlcy5vaWQ6XG4gICAgICAgICAgICByZXR1cm4gdG9OdW1iZXIodmFsdWUpO1xuICAgICAgICBjYXNlIFBvc3RncmVzVHlwZXMuanNvbjpcbiAgICAgICAgY2FzZSBQb3N0Z3Jlc1R5cGVzLmpzb25iOlxuICAgICAgICAgICAgcmV0dXJuIHRvSnNvbih2YWx1ZSk7XG4gICAgICAgIGNhc2UgUG9zdGdyZXNUeXBlcy50aW1lc3RhbXA6XG4gICAgICAgICAgICByZXR1cm4gdG9UaW1lc3RhbXBTdHJpbmcodmFsdWUpOyAvLyBGb3JtYXQgdG8gYmUgY29uc2lzdGVudCB3aXRoIFBvc3RnUkVTVFxuICAgICAgICBjYXNlIFBvc3RncmVzVHlwZXMuYWJzdGltZTogLy8gVG8gYWxsb3cgdXNlcnMgdG8gY2FzdCBpdCBiYXNlZCBvbiBUaW1lem9uZVxuICAgICAgICBjYXNlIFBvc3RncmVzVHlwZXMuZGF0ZTogLy8gVG8gYWxsb3cgdXNlcnMgdG8gY2FzdCBpdCBiYXNlZCBvbiBUaW1lem9uZVxuICAgICAgICBjYXNlIFBvc3RncmVzVHlwZXMuZGF0ZXJhbmdlOlxuICAgICAgICBjYXNlIFBvc3RncmVzVHlwZXMuaW50NHJhbmdlOlxuICAgICAgICBjYXNlIFBvc3RncmVzVHlwZXMuaW50OHJhbmdlOlxuICAgICAgICBjYXNlIFBvc3RncmVzVHlwZXMubW9uZXk6XG4gICAgICAgIGNhc2UgUG9zdGdyZXNUeXBlcy5yZWx0aW1lOiAvLyBUbyBhbGxvdyB1c2VycyB0byBjYXN0IGl0IGJhc2VkIG9uIFRpbWV6b25lXG4gICAgICAgIGNhc2UgUG9zdGdyZXNUeXBlcy50ZXh0OlxuICAgICAgICBjYXNlIFBvc3RncmVzVHlwZXMudGltZTogLy8gVG8gYWxsb3cgdXNlcnMgdG8gY2FzdCBpdCBiYXNlZCBvbiBUaW1lem9uZVxuICAgICAgICBjYXNlIFBvc3RncmVzVHlwZXMudGltZXN0YW1wdHo6IC8vIFRvIGFsbG93IHVzZXJzIHRvIGNhc3QgaXQgYmFzZWQgb24gVGltZXpvbmVcbiAgICAgICAgY2FzZSBQb3N0Z3Jlc1R5cGVzLnRpbWV0ejogLy8gVG8gYWxsb3cgdXNlcnMgdG8gY2FzdCBpdCBiYXNlZCBvbiBUaW1lem9uZVxuICAgICAgICBjYXNlIFBvc3RncmVzVHlwZXMudHNyYW5nZTpcbiAgICAgICAgY2FzZSBQb3N0Z3Jlc1R5cGVzLnRzdHpyYW5nZTpcbiAgICAgICAgICAgIHJldHVybiBub29wKHZhbHVlKTtcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIC8vIFJldHVybiB0aGUgdmFsdWUgZm9yIHJlbWFpbmluZyB0eXBlc1xuICAgICAgICAgICAgcmV0dXJuIG5vb3AodmFsdWUpO1xuICAgIH1cbn07XG5jb25zdCBub29wID0gKHZhbHVlKSA9PiB7XG4gICAgcmV0dXJuIHZhbHVlO1xufTtcbmV4cG9ydCBjb25zdCB0b0Jvb2xlYW4gPSAodmFsdWUpID0+IHtcbiAgICBzd2l0Y2ggKHZhbHVlKSB7XG4gICAgICAgIGNhc2UgJ3QnOlxuICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIGNhc2UgJ2YnOlxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgcmV0dXJuIHZhbHVlO1xuICAgIH1cbn07XG5leHBvcnQgY29uc3QgdG9OdW1iZXIgPSAodmFsdWUpID0+IHtcbiAgICBpZiAodHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJykge1xuICAgICAgICBjb25zdCBwYXJzZWRWYWx1ZSA9IHBhcnNlRmxvYXQodmFsdWUpO1xuICAgICAgICBpZiAoIU51bWJlci5pc05hTihwYXJzZWRWYWx1ZSkpIHtcbiAgICAgICAgICAgIHJldHVybiBwYXJzZWRWYWx1ZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gdmFsdWU7XG59O1xuZXhwb3J0IGNvbnN0IHRvSnNvbiA9ICh2YWx1ZSkgPT4ge1xuICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICByZXR1cm4gSlNPTi5wYXJzZSh2YWx1ZSk7XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhgSlNPTiBwYXJzZSBlcnJvcjogJHtlcnJvcn1gKTtcbiAgICAgICAgICAgIHJldHVybiB2YWx1ZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gdmFsdWU7XG59O1xuLyoqXG4gKiBDb252ZXJ0cyBhIFBvc3RncmVzIEFycmF5IGludG8gYSBuYXRpdmUgSlMgYXJyYXlcbiAqXG4gKiBAZXhhbXBsZSB0b0FycmF5KCd7fScsICdpbnQ0JylcbiAqIC8vPT4gW11cbiAqIEBleGFtcGxlIHRvQXJyYXkoJ3tcIlsyMDIxLTAxLTAxLDIwMjEtMTItMzEpXCIsXCIoMjAyMS0wMS0wMSwyMDIxLTEyLTMyXVwifScsICdkYXRlcmFuZ2UnKVxuICogLy89PiBbJ1syMDIxLTAxLTAxLDIwMjEtMTItMzEpJywgJygyMDIxLTAxLTAxLDIwMjEtMTItMzJdJ11cbiAqIEBleGFtcGxlIHRvQXJyYXkoWzEsMiwzLDRdLCAnaW50NCcpXG4gKiAvLz0+IFsxLDIsMyw0XVxuICovXG5leHBvcnQgY29uc3QgdG9BcnJheSA9ICh2YWx1ZSwgdHlwZSkgPT4ge1xuICAgIGlmICh0eXBlb2YgdmFsdWUgIT09ICdzdHJpbmcnKSB7XG4gICAgICAgIHJldHVybiB2YWx1ZTtcbiAgICB9XG4gICAgY29uc3QgbGFzdElkeCA9IHZhbHVlLmxlbmd0aCAtIDE7XG4gICAgY29uc3QgY2xvc2VCcmFjZSA9IHZhbHVlW2xhc3RJZHhdO1xuICAgIGNvbnN0IG9wZW5CcmFjZSA9IHZhbHVlWzBdO1xuICAgIC8vIENvbmZpcm0gdmFsdWUgaXMgYSBQb3N0Z3JlcyBhcnJheSBieSBjaGVja2luZyBjdXJseSBicmFja2V0c1xuICAgIGlmIChvcGVuQnJhY2UgPT09ICd7JyAmJiBjbG9zZUJyYWNlID09PSAnfScpIHtcbiAgICAgICAgbGV0IGFycjtcbiAgICAgICAgY29uc3QgdmFsVHJpbSA9IHZhbHVlLnNsaWNlKDEsIGxhc3RJZHgpO1xuICAgICAgICAvLyBUT0RPOiBmaW5kIGEgYmV0dGVyIHNvbHV0aW9uIHRvIHNlcGFyYXRlIFBvc3RncmVzIGFycmF5IGRhdGFcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGFyciA9IEpTT04ucGFyc2UoJ1snICsgdmFsVHJpbSArICddJyk7XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKF8pIHtcbiAgICAgICAgICAgIC8vIFdBUk5JTkc6IHNwbGl0dGluZyBvbiBjb21tYSBkb2VzIG5vdCBjb3ZlciBhbGwgZWRnZSBjYXNlc1xuICAgICAgICAgICAgYXJyID0gdmFsVHJpbSA/IHZhbFRyaW0uc3BsaXQoJywnKSA6IFtdO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBhcnIubWFwKCh2YWwpID0+IGNvbnZlcnRDZWxsKHR5cGUsIHZhbCkpO1xuICAgIH1cbiAgICByZXR1cm4gdmFsdWU7XG59O1xuLyoqXG4gKiBGaXhlcyB0aW1lc3RhbXAgdG8gYmUgSVNPLTg2MDEuIFN3YXBzIHRoZSBzcGFjZSBiZXR3ZWVuIHRoZSBkYXRlIGFuZCB0aW1lIGZvciBhICdUJ1xuICogU2VlIGh0dHBzOi8vZ2l0aHViLmNvbS9zdXBhYmFzZS9zdXBhYmFzZS9pc3N1ZXMvMThcbiAqXG4gKiBAZXhhbXBsZSB0b1RpbWVzdGFtcFN0cmluZygnMjAxOS0wOS0xMCAwMDowMDowMCcpXG4gKiAvLz0+ICcyMDE5LTA5LTEwVDAwOjAwOjAwJ1xuICovXG5leHBvcnQgY29uc3QgdG9UaW1lc3RhbXBTdHJpbmcgPSAodmFsdWUpID0+IHtcbiAgICBpZiAodHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJykge1xuICAgICAgICByZXR1cm4gdmFsdWUucmVwbGFjZSgnICcsICdUJyk7XG4gICAgfVxuICAgIHJldHVybiB2YWx1ZTtcbn07XG5leHBvcnQgY29uc3QgaHR0cEVuZHBvaW50VVJMID0gKHNvY2tldFVybCkgPT4ge1xuICAgIGxldCB1cmwgPSBzb2NrZXRVcmw7XG4gICAgdXJsID0gdXJsLnJlcGxhY2UoL153cy9pLCAnaHR0cCcpO1xuICAgIHVybCA9IHVybC5yZXBsYWNlKC8oXFwvc29ja2V0XFwvd2Vic29ja2V0fFxcL3NvY2tldHxcXC93ZWJzb2NrZXQpXFwvPyQvaSwgJycpO1xuICAgIHJldHVybiB1cmwucmVwbGFjZSgvXFwvKyQvLCAnJykgKyAnL2FwaS9icm9hZGNhc3QnO1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXRyYW5zZm9ybWVycy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/version.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/version.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.15.0';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9Ac3VwYWJhc2UrcmVhbHRpbWUtanNAMi4xNS4wL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvcmVhbHRpbWUtanMvZGlzdC9tb2R1bGUvbGliL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca29tYWxcXERvY3VtZW50c1xcanNtLWFwcHNcXG5leHRcXGVjb21cXHYwLWRvd25sb2FkLWF0dGVtcHRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStyZWFsdGltZS1qc0AyLjE1LjBcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxyZWFsdGltZS1qc1xcZGlzdFxcbW9kdWxlXFxsaWJcXHZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHZlcnNpb24gPSAnMi4xNS4wJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/version.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/websocket-factory.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/websocket-factory.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebSocketFactory: () => (/* binding */ WebSocketFactory),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nclass WebSocketFactory {\n    /**\n     * Dynamic require that works in both CJS and ESM environments\n     * Bulletproof against strict ESM environments where require might not be in scope\n     * @private\n     */\n    static dynamicRequire(moduleId) {\n        try {\n            // Check if we're in a Node.js environment first\n            if (typeof process !== 'undefined' &&\n                process.versions &&\n                process.versions.node) {\n                // In Node.js, both CJS and ESM support require for dynamic imports\n                // Wrap in try/catch to handle strict ESM environments\n                if (true) {\n                    return __webpack_require__(\"(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib sync recursive\")(moduleId);\n                }\n            }\n            return null;\n        }\n        catch (_a) {\n            // Catches any error from typeof require OR require() call in strict ESM\n            return null;\n        }\n    }\n    static detectEnvironment() {\n        var _a, _b;\n        if (typeof WebSocket !== 'undefined') {\n            return { type: 'native', constructor: WebSocket };\n        }\n        if (typeof globalThis !== 'undefined' &&\n            typeof globalThis.WebSocket !== 'undefined') {\n            return { type: 'native', constructor: globalThis.WebSocket };\n        }\n        if (typeof global !== 'undefined' &&\n            typeof global.WebSocket !== 'undefined') {\n            return { type: 'native', constructor: global.WebSocket };\n        }\n        if (typeof globalThis !== 'undefined' &&\n            typeof globalThis.WebSocketPair !== 'undefined' &&\n            typeof globalThis.WebSocket === 'undefined') {\n            return {\n                type: 'cloudflare',\n                error: 'Cloudflare Workers detected. WebSocket clients are not supported in Cloudflare Workers.',\n                workaround: 'Use Cloudflare Workers WebSocket API for server-side WebSocket handling, or deploy to a different runtime.',\n            };\n        }\n        if ((typeof globalThis !== 'undefined' && globalThis.EdgeRuntime) ||\n            (typeof navigator !== 'undefined' &&\n                ((_a = navigator.userAgent) === null || _a === void 0 ? void 0 : _a.includes('Vercel-Edge')))) {\n            return {\n                type: 'unsupported',\n                error: 'Edge runtime detected (Vercel Edge/Netlify Edge). WebSockets are not supported in edge functions.',\n                workaround: 'Use serverless functions or a different deployment target for WebSocket functionality.',\n            };\n        }\n        if (typeof process !== 'undefined' &&\n            process.versions &&\n            process.versions.node) {\n            const nodeVersion = parseInt(process.versions.node.split('.')[0]);\n            if (nodeVersion >= 22) {\n                try {\n                    if (typeof globalThis.WebSocket !== 'undefined') {\n                        return { type: 'native', constructor: globalThis.WebSocket };\n                    }\n                    const undici = this.dynamicRequire('undici');\n                    if (undici && undici.WebSocket) {\n                        return { type: 'native', constructor: undici.WebSocket };\n                    }\n                    throw new Error('undici not available');\n                }\n                catch (err) {\n                    return {\n                        type: 'unsupported',\n                        error: `Node.js ${nodeVersion} detected but native WebSocket not found.`,\n                        workaround: 'Install the \"ws\" package or check your Node.js installation.',\n                    };\n                }\n            }\n            try {\n                // Use dynamic require to work in both CJS and ESM environments\n                const ws = this.dynamicRequire('ws');\n                if (ws) {\n                    return { type: 'ws', constructor: (_b = ws.WebSocket) !== null && _b !== void 0 ? _b : ws };\n                }\n                throw new Error('ws package not available');\n            }\n            catch (err) {\n                return {\n                    type: 'unsupported',\n                    error: `Node.js ${nodeVersion} detected without WebSocket support.`,\n                    workaround: 'Install the \"ws\" package: npm install ws',\n                };\n            }\n        }\n        return {\n            type: 'unsupported',\n            error: 'Unknown JavaScript runtime without WebSocket support.',\n            workaround: \"Ensure you're running in a supported environment (browser, Node.js, Deno) or provide a custom WebSocket implementation.\",\n        };\n    }\n    static getWebSocketConstructor() {\n        const env = this.detectEnvironment();\n        if (env.constructor) {\n            return env.constructor;\n        }\n        let errorMessage = env.error || 'WebSocket not supported in this environment.';\n        if (env.workaround) {\n            errorMessage += `\\n\\nSuggested solution: ${env.workaround}`;\n        }\n        throw new Error(errorMessage);\n    }\n    static createWebSocket(url, protocols) {\n        const WS = this.getWebSocketConstructor();\n        return new WS(url, protocols);\n    }\n    static isWebSocketSupported() {\n        try {\n            const env = this.detectEnvironment();\n            return env.type === 'native' || env.type === 'ws';\n        }\n        catch (_a) {\n            return false;\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WebSocketFactory);\n//# sourceMappingURL=websocket-factory.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/lib/websocket-factory.js\n");

/***/ })

};
;