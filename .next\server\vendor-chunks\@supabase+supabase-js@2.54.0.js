"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+supabase-js@2.54.0";
exports.ids = ["vendor-chunks/@supabase+supabase-js@2.54.0"];
exports.modules = {

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_functions_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @supabase/functions-js */ \"(action-browser)/./node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/FunctionsClient.js\");\n/* harmony import */ var _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/postgrest-js */ \"(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs\");\n/* harmony import */ var _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/realtime-js */ \"(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/index.js\");\n/* harmony import */ var _supabase_storage_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @supabase/storage-js */ \"(action-browser)/./node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/StorageClient.js\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/constants */ \"(action-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_fetch__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/fetch */ \"(action-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/helpers */ \"(action-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js\");\n/* harmony import */ var _lib_SupabaseAuthClient__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/SupabaseAuthClient */ \"(action-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\n\n\n\n\n\n\n/**\n * Supabase Client.\n *\n * An isomorphic Javascript client for interacting with Postgres.\n */\nclass SupabaseClient {\n    /**\n     * Create a new client for use in the browser.\n     * @param supabaseUrl The unique Supabase URL which is supplied when you create a new project in your project dashboard.\n     * @param supabaseKey The unique Supabase Key which is supplied when you create a new project in your project dashboard.\n     * @param options.db.schema You can switch in between schemas. The schema needs to be on the list of exposed schemas inside Supabase.\n     * @param options.auth.autoRefreshToken Set to \"true\" if you want to automatically refresh the token before expiring.\n     * @param options.auth.persistSession Set to \"true\" if you want to automatically save the user session into local storage.\n     * @param options.auth.detectSessionInUrl Set to \"true\" if you want to automatically detects OAuth grants in the URL and signs in the user.\n     * @param options.realtime Options passed along to realtime-js constructor.\n     * @param options.storage Options passed along to the storage-js constructor.\n     * @param options.global.fetch A custom fetch implementation.\n     * @param options.global.headers Any additional headers to send with each network request.\n     */\n    constructor(supabaseUrl, supabaseKey, options) {\n        var _a, _b, _c;\n        this.supabaseUrl = supabaseUrl;\n        this.supabaseKey = supabaseKey;\n        if (!supabaseUrl)\n            throw new Error('supabaseUrl is required.');\n        if (!supabaseKey)\n            throw new Error('supabaseKey is required.');\n        const _supabaseUrl = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_2__.ensureTrailingSlash)(supabaseUrl);\n        const baseUrl = new URL(_supabaseUrl);\n        this.realtimeUrl = new URL('realtime/v1', baseUrl);\n        this.realtimeUrl.protocol = this.realtimeUrl.protocol.replace('http', 'ws');\n        this.authUrl = new URL('auth/v1', baseUrl);\n        this.storageUrl = new URL('storage/v1', baseUrl);\n        this.functionsUrl = new URL('functions/v1', baseUrl);\n        // default storage key uses the supabase project ref as a namespace\n        const defaultStorageKey = `sb-${baseUrl.hostname.split('.')[0]}-auth-token`;\n        const DEFAULTS = {\n            db: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_DB_OPTIONS,\n            realtime: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_REALTIME_OPTIONS,\n            auth: Object.assign(Object.assign({}, _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_AUTH_OPTIONS), { storageKey: defaultStorageKey }),\n            global: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_GLOBAL_OPTIONS,\n        };\n        const settings = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_2__.applySettingDefaults)(options !== null && options !== void 0 ? options : {}, DEFAULTS);\n        this.storageKey = (_a = settings.auth.storageKey) !== null && _a !== void 0 ? _a : '';\n        this.headers = (_b = settings.global.headers) !== null && _b !== void 0 ? _b : {};\n        if (!settings.accessToken) {\n            this.auth = this._initSupabaseAuthClient((_c = settings.auth) !== null && _c !== void 0 ? _c : {}, this.headers, settings.global.fetch);\n        }\n        else {\n            this.accessToken = settings.accessToken;\n            this.auth = new Proxy({}, {\n                get: (_, prop) => {\n                    throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(prop)} is not possible`);\n                },\n            });\n        }\n        this.fetch = (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_4__.fetchWithAuth)(supabaseKey, this._getAccessToken.bind(this), settings.global.fetch);\n        this.realtime = this._initRealtimeClient(Object.assign({ headers: this.headers, accessToken: this._getAccessToken.bind(this) }, settings.realtime));\n        this.rest = new _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_0__.PostgrestClient(new URL('rest/v1', baseUrl).href, {\n            headers: this.headers,\n            schema: settings.db.schema,\n            fetch: this.fetch,\n        });\n        this.storage = new _supabase_storage_js__WEBPACK_IMPORTED_MODULE_5__.StorageClient(this.storageUrl.href, this.headers, this.fetch, options === null || options === void 0 ? void 0 : options.storage);\n        if (!settings.accessToken) {\n            this._listenForAuthEvents();\n        }\n    }\n    /**\n     * Supabase Functions allows you to deploy and invoke edge functions.\n     */\n    get functions() {\n        return new _supabase_functions_js__WEBPACK_IMPORTED_MODULE_6__.FunctionsClient(this.functionsUrl.href, {\n            headers: this.headers,\n            customFetch: this.fetch,\n        });\n    }\n    /**\n     * Perform a query on a table or a view.\n     *\n     * @param relation - The table or view name to query\n     */\n    from(relation) {\n        return this.rest.from(relation);\n    }\n    // NOTE: signatures must be kept in sync with PostgrestClient.schema\n    /**\n     * Select a schema to query or perform an function (rpc) call.\n     *\n     * The schema needs to be on the list of exposed schemas inside Supabase.\n     *\n     * @param schema - The schema to query\n     */\n    schema(schema) {\n        return this.rest.schema(schema);\n    }\n    // NOTE: signatures must be kept in sync with PostgrestClient.rpc\n    /**\n     * Perform a function call.\n     *\n     * @param fn - The function name to call\n     * @param args - The arguments to pass to the function call\n     * @param options - Named parameters\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     * @param options.get - When set to `true`, the function will be called with\n     * read-only access mode.\n     * @param options.count - Count algorithm to use to count rows returned by the\n     * function. Only applicable for [set-returning\n     * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    rpc(fn, args = {}, options = {}) {\n        return this.rest.rpc(fn, args, options);\n    }\n    /**\n     * Creates a Realtime channel with Broadcast, Presence, and Postgres Changes.\n     *\n     * @param {string} name - The name of the Realtime channel.\n     * @param {Object} opts - The options to pass to the Realtime channel.\n     *\n     */\n    channel(name, opts = { config: {} }) {\n        return this.realtime.channel(name, opts);\n    }\n    /**\n     * Returns all Realtime channels.\n     */\n    getChannels() {\n        return this.realtime.getChannels();\n    }\n    /**\n     * Unsubscribes and removes Realtime channel from Realtime client.\n     *\n     * @param {RealtimeChannel} channel - The name of the Realtime channel.\n     *\n     */\n    removeChannel(channel) {\n        return this.realtime.removeChannel(channel);\n    }\n    /**\n     * Unsubscribes and removes all Realtime channels from Realtime client.\n     */\n    removeAllChannels() {\n        return this.realtime.removeAllChannels();\n    }\n    _getAccessToken() {\n        var _a, _b;\n        return __awaiter(this, void 0, void 0, function* () {\n            if (this.accessToken) {\n                return yield this.accessToken();\n            }\n            const { data } = yield this.auth.getSession();\n            return (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : this.supabaseKey;\n        });\n    }\n    _initSupabaseAuthClient({ autoRefreshToken, persistSession, detectSessionInUrl, storage, storageKey, flowType, lock, debug, }, headers, fetch) {\n        const authHeaders = {\n            Authorization: `Bearer ${this.supabaseKey}`,\n            apikey: `${this.supabaseKey}`,\n        };\n        return new _lib_SupabaseAuthClient__WEBPACK_IMPORTED_MODULE_7__.SupabaseAuthClient({\n            url: this.authUrl.href,\n            headers: Object.assign(Object.assign({}, authHeaders), headers),\n            storageKey: storageKey,\n            autoRefreshToken,\n            persistSession,\n            detectSessionInUrl,\n            storage,\n            flowType,\n            lock,\n            debug,\n            fetch,\n            // auth checks if there is a custom authorizaiton header using this flag\n            // so it knows whether to return an error when getUser is called with no session\n            hasCustomAuthorizationHeader: 'Authorization' in this.headers,\n        });\n    }\n    _initRealtimeClient(options) {\n        return new _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_1__.RealtimeClient(this.realtimeUrl.href, Object.assign(Object.assign({}, options), { params: Object.assign({ apikey: this.supabaseKey }, options === null || options === void 0 ? void 0 : options.params) }));\n    }\n    _listenForAuthEvents() {\n        let data = this.auth.onAuthStateChange((event, session) => {\n            this._handleTokenChanged(event, 'CLIENT', session === null || session === void 0 ? void 0 : session.access_token);\n        });\n        return data;\n    }\n    _handleTokenChanged(event, source, token) {\n        if ((event === 'TOKEN_REFRESHED' || event === 'SIGNED_IN') &&\n            this.changedAccessToken !== token) {\n            this.changedAccessToken = token;\n        }\n        else if (event === 'SIGNED_OUT') {\n            this.realtime.setAuth();\n            if (source == 'STORAGE')\n                this.auth.signOut();\n            this.changedAccessToken = undefined;\n        }\n    }\n}\n//# sourceMappingURL=SupabaseClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/index.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/index.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthAdminApi: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthAdminApi),\n/* harmony export */   AuthApiError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthApiError),\n/* harmony export */   AuthClient: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthClient),\n/* harmony export */   AuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthError),\n/* harmony export */   AuthImplicitGrantRedirectError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthImplicitGrantRedirectError),\n/* harmony export */   AuthInvalidCredentialsError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidCredentialsError),\n/* harmony export */   AuthInvalidJwtError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidJwtError),\n/* harmony export */   AuthInvalidTokenResponseError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidTokenResponseError),\n/* harmony export */   AuthPKCEGrantCodeExchangeError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthPKCEGrantCodeExchangeError),\n/* harmony export */   AuthRetryableFetchError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthRetryableFetchError),\n/* harmony export */   AuthSessionMissingError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthSessionMissingError),\n/* harmony export */   AuthUnknownError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthUnknownError),\n/* harmony export */   AuthWeakPasswordError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthWeakPasswordError),\n/* harmony export */   CustomAuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.CustomAuthError),\n/* harmony export */   FunctionRegion: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionRegion),\n/* harmony export */   FunctionsError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsError),\n/* harmony export */   FunctionsFetchError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsFetchError),\n/* harmony export */   FunctionsHttpError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsHttpError),\n/* harmony export */   FunctionsRelayError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsRelayError),\n/* harmony export */   GoTrueAdminApi: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.GoTrueAdminApi),\n/* harmony export */   GoTrueClient: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.GoTrueClient),\n/* harmony export */   NavigatorLockAcquireTimeoutError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.NavigatorLockAcquireTimeoutError),\n/* harmony export */   PostgrestError: () => (/* reexport safe */ _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_1__.PostgrestError),\n/* harmony export */   REALTIME_CHANNEL_STATES: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_CHANNEL_STATES),\n/* harmony export */   REALTIME_LISTEN_TYPES: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_LISTEN_TYPES),\n/* harmony export */   REALTIME_POSTGRES_CHANGES_LISTEN_EVENT: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_POSTGRES_CHANGES_LISTEN_EVENT),\n/* harmony export */   REALTIME_PRESENCE_LISTEN_EVENTS: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_PRESENCE_LISTEN_EVENTS),\n/* harmony export */   REALTIME_SUBSCRIBE_STATES: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_SUBSCRIBE_STATES),\n/* harmony export */   RealtimeChannel: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.RealtimeChannel),\n/* harmony export */   RealtimeClient: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.RealtimeClient),\n/* harmony export */   RealtimePresence: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.RealtimePresence),\n/* harmony export */   SIGN_OUT_SCOPES: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.SIGN_OUT_SCOPES),\n/* harmony export */   SupabaseClient: () => (/* reexport safe */ _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   WebSocketFactory: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.WebSocketFactory),\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   isAuthApiError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthApiError),\n/* harmony export */   isAuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthError),\n/* harmony export */   isAuthImplicitGrantRedirectError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthImplicitGrantRedirectError),\n/* harmony export */   isAuthRetryableFetchError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthRetryableFetchError),\n/* harmony export */   isAuthSessionMissingError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthSessionMissingError),\n/* harmony export */   isAuthWeakPasswordError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthWeakPasswordError),\n/* harmony export */   lockInternals: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.lockInternals),\n/* harmony export */   navigatorLock: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.navigatorLock),\n/* harmony export */   processLock: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.processLock)\n/* harmony export */ });\n/* harmony import */ var _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SupabaseClient */ \"(action-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js\");\n/* harmony import */ var _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-js */ \"(action-browser)/./node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/index.js\");\n/* harmony import */ var _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/postgrest-js */ \"(action-browser)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs\");\n/* harmony import */ var _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/functions-js */ \"(action-browser)/./node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/types.js\");\n/* harmony import */ var _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/realtime-js */ \"(action-browser)/./node_modules/.pnpm/@supabase+realtime-js@2.15.0/node_modules/@supabase/realtime-js/dist/module/index.js\");\n\n\n\n\n\n\n/**\n * Creates a new Supabase Client.\n */\nconst createClient = (supabaseUrl, supabaseKey, options) => {\n    return new _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__[\"default\"](supabaseUrl, supabaseKey, options);\n};\n// Check for Node.js <= 18 deprecation\nfunction shouldShowDeprecationWarning() {\n    // Skip in browser environments\n    if (typeof window !== 'undefined') {\n        return false;\n    }\n    // Skip if process is not available (e.g., Edge Runtime)\n    if (typeof process === 'undefined') {\n        return false;\n    }\n    // Use dynamic property access to avoid Next.js Edge Runtime static analysis warnings\n    const processVersion = process['version'];\n    if (processVersion === undefined || processVersion === null) {\n        return false;\n    }\n    const versionMatch = processVersion.match(/^v(\\d+)\\./);\n    if (!versionMatch) {\n        return false;\n    }\n    const majorVersion = parseInt(versionMatch[1], 10);\n    return majorVersion <= 18;\n}\nif (shouldShowDeprecationWarning()) {\n    console.warn(`⚠️  Node.js 18 and below are deprecated and will no longer be supported in future versions of @supabase/supabase-js. ` +\n        `Please upgrade to Node.js 20 or later. ` +\n        `For more information, visit: https://github.com/orgs/supabase/discussions/37217`);\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAuthClient: () => (/* binding */ SupabaseAuthClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-js */ \"(action-browser)/./node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/index.js\");\n\nclass SupabaseAuthClient extends _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthClient {\n    constructor(options) {\n        super(options);\n    }\n}\n//# sourceMappingURL=SupabaseAuthClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9Ac3VwYWJhc2Urc3VwYWJhc2UtanNAMi41NC4wL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2Uvc3VwYWJhc2UtanMvZGlzdC9tb2R1bGUvbGliL1N1cGFiYXNlQXV0aENsaWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUErQztBQUN4QyxpQ0FBaUMseURBQVU7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxrb21hbFxcRG9jdW1lbnRzXFxqc20tYXBwc1xcbmV4dFxcZWNvbVxcdjAtZG93bmxvYWQtYXR0ZW1wdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTQuMFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHN1cGFiYXNlLWpzXFxkaXN0XFxtb2R1bGVcXGxpYlxcU3VwYWJhc2VBdXRoQ2xpZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEF1dGhDbGllbnQgfSBmcm9tICdAc3VwYWJhc2UvYXV0aC1qcyc7XG5leHBvcnQgY2xhc3MgU3VwYWJhc2VBdXRoQ2xpZW50IGV4dGVuZHMgQXV0aENsaWVudCB7XG4gICAgY29uc3RydWN0b3Iob3B0aW9ucykge1xuICAgICAgICBzdXBlcihvcHRpb25zKTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1TdXBhYmFzZUF1dGhDbGllbnQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/lib/constants.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/lib/constants.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_AUTH_OPTIONS: () => (/* binding */ DEFAULT_AUTH_OPTIONS),\n/* harmony export */   DEFAULT_DB_OPTIONS: () => (/* binding */ DEFAULT_DB_OPTIONS),\n/* harmony export */   DEFAULT_GLOBAL_OPTIONS: () => (/* binding */ DEFAULT_GLOBAL_OPTIONS),\n/* harmony export */   DEFAULT_HEADERS: () => (/* binding */ DEFAULT_HEADERS),\n/* harmony export */   DEFAULT_REALTIME_OPTIONS: () => (/* binding */ DEFAULT_REALTIME_OPTIONS)\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(action-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/lib/version.js\");\n\nlet JS_ENV = '';\n// @ts-ignore\nif (typeof Deno !== 'undefined') {\n    JS_ENV = 'deno';\n}\nelse if (typeof document !== 'undefined') {\n    JS_ENV = 'web';\n}\nelse if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {\n    JS_ENV = 'react-native';\n}\nelse {\n    JS_ENV = 'node';\n}\nconst DEFAULT_HEADERS = { 'X-Client-Info': `supabase-js-${JS_ENV}/${_version__WEBPACK_IMPORTED_MODULE_0__.version}` };\nconst DEFAULT_GLOBAL_OPTIONS = {\n    headers: DEFAULT_HEADERS,\n};\nconst DEFAULT_DB_OPTIONS = {\n    schema: 'public',\n};\nconst DEFAULT_AUTH_OPTIONS = {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true,\n    flowType: 'implicit',\n};\nconst DEFAULT_REALTIME_OPTIONS = {};\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9Ac3VwYWJhc2Urc3VwYWJhc2UtanNAMi41NC4wL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2Uvc3VwYWJhc2UtanMvZGlzdC9tb2R1bGUvbGliL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBb0M7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPLDBCQUEwQixnQ0FBZ0MsT0FBTyxHQUFHLDZDQUFPLENBQUM7QUFDNUU7QUFDUDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxrb21hbFxcRG9jdW1lbnRzXFxqc20tYXBwc1xcbmV4dFxcZWNvbVxcdjAtZG93bmxvYWQtYXR0ZW1wdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTQuMFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHN1cGFiYXNlLWpzXFxkaXN0XFxtb2R1bGVcXGxpYlxcY29uc3RhbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHZlcnNpb24gfSBmcm9tICcuL3ZlcnNpb24nO1xubGV0IEpTX0VOViA9ICcnO1xuLy8gQHRzLWlnbm9yZVxuaWYgKHR5cGVvZiBEZW5vICE9PSAndW5kZWZpbmVkJykge1xuICAgIEpTX0VOViA9ICdkZW5vJztcbn1cbmVsc2UgaWYgKHR5cGVvZiBkb2N1bWVudCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICBKU19FTlYgPSAnd2ViJztcbn1cbmVsc2UgaWYgKHR5cGVvZiBuYXZpZ2F0b3IgIT09ICd1bmRlZmluZWQnICYmIG5hdmlnYXRvci5wcm9kdWN0ID09PSAnUmVhY3ROYXRpdmUnKSB7XG4gICAgSlNfRU5WID0gJ3JlYWN0LW5hdGl2ZSc7XG59XG5lbHNlIHtcbiAgICBKU19FTlYgPSAnbm9kZSc7XG59XG5leHBvcnQgY29uc3QgREVGQVVMVF9IRUFERVJTID0geyAnWC1DbGllbnQtSW5mbyc6IGBzdXBhYmFzZS1qcy0ke0pTX0VOVn0vJHt2ZXJzaW9ufWAgfTtcbmV4cG9ydCBjb25zdCBERUZBVUxUX0dMT0JBTF9PUFRJT05TID0ge1xuICAgIGhlYWRlcnM6IERFRkFVTFRfSEVBREVSUyxcbn07XG5leHBvcnQgY29uc3QgREVGQVVMVF9EQl9PUFRJT05TID0ge1xuICAgIHNjaGVtYTogJ3B1YmxpYycsXG59O1xuZXhwb3J0IGNvbnN0IERFRkFVTFRfQVVUSF9PUFRJT05TID0ge1xuICAgIGF1dG9SZWZyZXNoVG9rZW46IHRydWUsXG4gICAgcGVyc2lzdFNlc3Npb246IHRydWUsXG4gICAgZGV0ZWN0U2Vzc2lvbkluVXJsOiB0cnVlLFxuICAgIGZsb3dUeXBlOiAnaW1wbGljaXQnLFxufTtcbmV4cG9ydCBjb25zdCBERUZBVUxUX1JFQUxUSU1FX09QVElPTlMgPSB7fTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbnN0YW50cy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/lib/constants.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchWithAuth: () => (/* binding */ fetchWithAuth),\n/* harmony export */   resolveFetch: () => (/* binding */ resolveFetch),\n/* harmony export */   resolveHeadersConstructor: () => (/* binding */ resolveHeadersConstructor)\n/* harmony export */ });\n/* harmony import */ var _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/node-fetch */ \"(action-browser)/./node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\");\n/* harmony import */ var _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__);\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n// @ts-ignore\n\nconst resolveFetch = (customFetch) => {\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    }\n    else if (typeof fetch === 'undefined') {\n        _fetch = (_supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0___default());\n    }\n    else {\n        _fetch = fetch;\n    }\n    return (...args) => _fetch(...args);\n};\nconst resolveHeadersConstructor = () => {\n    if (typeof Headers === 'undefined') {\n        return _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__.Headers;\n    }\n    return Headers;\n};\nconst fetchWithAuth = (supabaseKey, getAccessToken, customFetch) => {\n    const fetch = resolveFetch(customFetch);\n    const HeadersConstructor = resolveHeadersConstructor();\n    return (input, init) => __awaiter(void 0, void 0, void 0, function* () {\n        var _a;\n        const accessToken = (_a = (yield getAccessToken())) !== null && _a !== void 0 ? _a : supabaseKey;\n        let headers = new HeadersConstructor(init === null || init === void 0 ? void 0 : init.headers);\n        if (!headers.has('apikey')) {\n            headers.set('apikey', supabaseKey);\n        }\n        if (!headers.has('Authorization')) {\n            headers.set('Authorization', `Bearer ${accessToken}`);\n        }\n        return fetch(input, Object.assign(Object.assign({}, init), { headers }));\n    });\n};\n//# sourceMappingURL=fetch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applySettingDefaults: () => (/* binding */ applySettingDefaults),\n/* harmony export */   ensureTrailingSlash: () => (/* binding */ ensureTrailingSlash),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   uuid: () => (/* binding */ uuid)\n/* harmony export */ });\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nfunction uuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        var r = (Math.random() * 16) | 0, v = c == 'x' ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n    });\n}\nfunction ensureTrailingSlash(url) {\n    return url.endsWith('/') ? url : url + '/';\n}\nconst isBrowser = () => typeof window !== 'undefined';\nfunction applySettingDefaults(options, defaults) {\n    var _a, _b;\n    const { db: dbOptions, auth: authOptions, realtime: realtimeOptions, global: globalOptions, } = options;\n    const { db: DEFAULT_DB_OPTIONS, auth: DEFAULT_AUTH_OPTIONS, realtime: DEFAULT_REALTIME_OPTIONS, global: DEFAULT_GLOBAL_OPTIONS, } = defaults;\n    const result = {\n        db: Object.assign(Object.assign({}, DEFAULT_DB_OPTIONS), dbOptions),\n        auth: Object.assign(Object.assign({}, DEFAULT_AUTH_OPTIONS), authOptions),\n        realtime: Object.assign(Object.assign({}, DEFAULT_REALTIME_OPTIONS), realtimeOptions),\n        storage: {},\n        global: Object.assign(Object.assign(Object.assign({}, DEFAULT_GLOBAL_OPTIONS), globalOptions), { headers: Object.assign(Object.assign({}, ((_a = DEFAULT_GLOBAL_OPTIONS === null || DEFAULT_GLOBAL_OPTIONS === void 0 ? void 0 : DEFAULT_GLOBAL_OPTIONS.headers) !== null && _a !== void 0 ? _a : {})), ((_b = globalOptions === null || globalOptions === void 0 ? void 0 : globalOptions.headers) !== null && _b !== void 0 ? _b : {})) }),\n        accessToken: () => __awaiter(this, void 0, void 0, function* () { return ''; }),\n    };\n    if (options.accessToken) {\n        result.accessToken = options.accessToken;\n    }\n    else {\n        // hack around Required<>\n        delete result.accessToken;\n    }\n    return result;\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/lib/version.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/lib/version.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.54.0';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9Ac3VwYWJhc2Urc3VwYWJhc2UtanNAMi41NC4wL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2Uvc3VwYWJhc2UtanMvZGlzdC9tb2R1bGUvbGliL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca29tYWxcXERvY3VtZW50c1xcanNtLWFwcHNcXG5leHRcXGVjb21cXHYwLWRvd25sb2FkLWF0dGVtcHRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStzdXBhYmFzZS1qc0AyLjU0LjBcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxzdXBhYmFzZS1qc1xcZGlzdFxcbW9kdWxlXFxsaWJcXHZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHZlcnNpb24gPSAnMi41NC4wJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.54.0/node_modules/@supabase/supabase-js/dist/module/lib/version.js\n");

/***/ })

};
;