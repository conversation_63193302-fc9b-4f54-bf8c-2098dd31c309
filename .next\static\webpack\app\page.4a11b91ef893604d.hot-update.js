"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronLeft,ChevronRight,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronLeft,ChevronRight,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronLeft,ChevronRight,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronLeft,ChevronRight,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronLeft,ChevronRight,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_product_product_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/product/product-card */ \"(app-pages-browser)/./components/product/product-card.tsx\");\n/* harmony import */ var _app_admin_products_actions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/admin/products/actions */ \"(app-pages-browser)/./app/admin/products/actions.ts\");\n/* harmony import */ var _components_ui_loading_spinner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/loading-spinner */ \"(app-pages-browser)/./components/ui/loading-spinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n // Import from admin actions\n\nfunction HomePage() {\n    _s();\n    const [currentDeal, setCurrentDeal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [featuredProducts, setFeaturedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            const fetchFeaturedProducts = {\n                \"HomePage.useEffect.fetchFeaturedProducts\": async ()=>{\n                    setLoading(true);\n                    setError(null);\n                    const { products: fetchedProducts, error: fetchError } = await (0,_app_admin_products_actions__WEBPACK_IMPORTED_MODULE_6__.getProducts)();\n                    if (fetchError) {\n                        setError(fetchError);\n                    // toast.error(`Failed to load featured products: ${fetchError}`); // Consider adding toast\n                    } else if (fetchedProducts) {\n                        setFeaturedProducts(fetchedProducts);\n                    }\n                    setLoading(false);\n                }\n            }[\"HomePage.useEffect.fetchFeaturedProducts\"];\n            fetchFeaturedProducts();\n        }\n    }[\"HomePage.useEffect\"], []);\n    const newDeals = featuredProducts.slice(0, 2) // Use fetched products for new deals\n    ;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loading_spinner__WEBPACK_IMPORTED_MODULE_7__.LoadingSpinner, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n            lineNumber: 38,\n            columnNumber: 12\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-red-500 text-center py-16\",\n            children: [\n                \"Error loading homepage products: \",\n                error\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n            lineNumber: 42,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white pt-20 md:pt-24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"block\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full px-6 py-16 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 z-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: \"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1920&h=1080&fit=crop&crop=center\",\n                                    alt: \"Luxury Fashion Store\",\n                                    fill: true,\n                                    className: \"object-cover\",\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-black/40\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10 text-white\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-16 animate-in slide-in-from-top-8 duration-1000\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-6xl font-bold mb-6\",\n                                        children: \"Premium Leather Collection\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg md:text-xl text-gray-200 max-w-3xl mx-auto leading-relaxed mb-8\",\n                                        children: \"Discover handcrafted leather jackets made from the finest materials. Each piece combines timeless design with modern craftsmanship.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-8 text-sm text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Premium Quality\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Handcrafted\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2 fill-yellow-400 text-yellow-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"4.9 Rating\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/products\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                size: \"lg\",\n                                                className: \"bg-white text-gray-900 hover:bg-gray-100 px-8 py-3 text-lg font-semibold\",\n                                                children: \"Shop Collection\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16\",\n                            children: [\n                                newDeals.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group relative bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 rounded-2xl overflow-hidden card-hover border border-orange-200/50 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 md:p-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col md:flex-row md:items-center md:justify-between h-full min-h-[280px]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 mb-6 md:mb-0 md:pr-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-orange-500 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 109,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-2xl md:text-3xl font-bold text-gray-900\",\n                                                                    children: \"New Deals\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 110,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-3xl md:text-4xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        newDeals[currentDeal].price\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 114,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-700 text-lg font-medium\",\n                                                                    children: newDeals[currentDeal].name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 117,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-4 w-4 fill-yellow-400 text-yellow-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 121,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-sm font-medium text-gray-700\",\n                                                                    children: newDeals[currentDeal].rating\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 122,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-sm text-gray-500\",\n                                                                    children: \"(127 reviews)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 123,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"bg-white/80 hover:bg-white border-orange-200\",\n                                                                    onClick: ()=>setCurrentDeal(currentDeal === 0 ? newDeals.length - 1 : currentDeal + 1),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 133,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 127,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: \"Switch deals\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 135,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"bg-white/80 hover:bg-white border-orange-200\",\n                                                                    onClick: ()=>setCurrentDeal(currentDeal === newDeals.length - 1 ? 0 : currentDeal + 1),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 142,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 136,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-full md:w-48 h-48 md:h-56 relative\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: newDeals[currentDeal].image || \"/placeholder.svg\",\n                                                        alt: newDeals[currentDeal].name,\n                                                        fill: true,\n                                                        className: \"object-cover rounded-xl shadow-lg group-hover:scale-105 transition-transform duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group relative bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100 rounded-2xl overflow-hidden card-hover border border-gray-200/50 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 md:p-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col md:flex-row md:items-center md:justify-between h-full min-h-[280px]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 mb-6 md:mb-0 md:pr-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-2xl md:text-3xl font-bold text-gray-900 mb-4\",\n                                                            children: \"Premium Collection\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-6 text-base md:text-lg leading-relaxed\",\n                                                            children: \"Exceptional quality leather jackets crafted by master artisans\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-4 w-4 fill-yellow-400 text-yellow-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-sm font-medium text-gray-700\",\n                                                                    children: \"4.9\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-sm text-gray-500\",\n                                                                    children: \"(500+ reviews)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                            href: \"/products\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"bg-gray-900 hover:bg-gray-800 text-white\",\n                                                                children: \"View Collection\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                lineNumber: 177,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-full md:w-48 h-48 md:h-56 relative\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: \"https://images.unsplash.com/photo-1520975954732-35dd22299614?w=300&h=300&fit=crop&crop=center\",\n                                                        alt: \"Premium Leather Jacket\",\n                                                        fill: true,\n                                                        className: \"object-cover rounded-xl shadow-lg group-hover:scale-105 transition-transform duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group relative bg-white rounded-2xl p-8 border border-gray-100 card-hover shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-900 text-white px-3 py-1 rounded-full text-xs font-medium inline-block mb-4\",\n                                                        children: \"EXCLUSIVE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-gray-900 mb-2\",\n                                                        children: \"Signature Series\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-4\",\n                                                        children: \"Limited edition handcrafted pieces\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        size: \"sm\",\n                                                        className: \"bg-gray-900 hover:bg-gray-800\",\n                                                        children: \"Explore\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-24 h-24 relative ml-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    src: \"https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=150&h=150&fit=crop&crop=center\",\n                                                    alt: \"Signature Series\",\n                                                    fill: true,\n                                                    className: \"object-cover rounded-lg shadow-md\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group relative bg-white rounded-2xl p-8 border border-gray-100 card-hover shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-gray-900 mb-2\",\n                                                        children: \"Heritage Collection\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-4\",\n                                                        children: \"Classic designs with modern comfort\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        children: \"View All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-24 h-24 relative ml-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    src: \"https://images.unsplash.com/photo-1551028719-00167b16eac5?w=150&h=150&fit=crop&crop=center\",\n                                                    alt: \"Heritage Collection\",\n                                                    fill: true,\n                                                    className: \"object-cover rounded-lg shadow-md\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-12 animate-in slide-in-from-bottom-4 duration-1000\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                            children: \"Featured Products\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                                            children: \"Carefully selected pieces from our premium collection\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6\",\n                                        children: featuredProducts.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-in slide-in-from-bottom-4\",\n                                                style: {\n                                                    animationDelay: \"\".concat(index * 100, \"ms\"),\n                                                    animationDuration: \"600ms\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_product_card__WEBPACK_IMPORTED_MODULE_5__.ProductCard, {\n                                                    product: product\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, product.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mt-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/products\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            size: \"lg\",\n                                            variant: \"outline\",\n                                            className: \"bg-white hover:bg-gray-50\",\n                                            children: \"View All Products\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"12V8o694pBYMS4aZ8T6rNPh/ZWE=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});