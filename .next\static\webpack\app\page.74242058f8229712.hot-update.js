"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronLeft,ChevronRight,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronLeft,ChevronRight,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronLeft,ChevronRight,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronLeft,ChevronRight,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronLeft,ChevronRight,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_product_product_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/product/product-card */ \"(app-pages-browser)/./components/product/product-card.tsx\");\n/* harmony import */ var _app_admin_products_actions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/admin/products/actions */ \"(app-pages-browser)/./app/admin/products/actions.ts\");\n/* harmony import */ var _components_ui_loading_spinner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/loading-spinner */ \"(app-pages-browser)/./components/ui/loading-spinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n // Import from admin actions\n\nfunction HomePage() {\n    _s();\n    const [currentDeal, setCurrentDeal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [featuredProducts, setFeaturedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            const fetchFeaturedProducts = {\n                \"HomePage.useEffect.fetchFeaturedProducts\": async ()=>{\n                    setLoading(true);\n                    setError(null);\n                    const { products: fetchedProducts, error: fetchError } = await (0,_app_admin_products_actions__WEBPACK_IMPORTED_MODULE_6__.getProducts)();\n                    if (fetchError) {\n                        setError(fetchError);\n                    // toast.error(`Failed to load featured products: ${fetchError}`); // Consider adding toast\n                    } else if (fetchedProducts) {\n                        setFeaturedProducts(fetchedProducts);\n                    }\n                    setLoading(false);\n                }\n            }[\"HomePage.useEffect.fetchFeaturedProducts\"];\n            fetchFeaturedProducts();\n        }\n    }[\"HomePage.useEffect\"], []);\n    const newDeals = featuredProducts.slice(0, 2) // Use fetched products for new deals\n    ;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loading_spinner__WEBPACK_IMPORTED_MODULE_7__.LoadingSpinner, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n            lineNumber: 38,\n            columnNumber: 12\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-red-500 text-center py-16\",\n            children: [\n                \"Error loading homepage products: \",\n                error\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n            lineNumber: 42,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white pt-20 md:pt-24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"block\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full px-6 py-16 flex items-center justify-center min-h-[80vh]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full max-w-7xl mx-auto rounded-3xl overflow-hidden shadow-2xl transform perspective-1000 hover:scale-[1.02] transition-transform duration-700 ease-out\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 z-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: \"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1920&h=1080&fit=crop&crop=center\",\n                                        alt: \"Luxury Fashion Store\",\n                                        fill: true,\n                                        className: \"object-cover rounded-3xl\",\n                                        priority: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-br from-black/30 via-black/40 to-black/60 rounded-3xl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 rounded-3xl shadow-inner shadow-black/20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10 px-8 py-20 md:py-32 text-center text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-in slide-in-from-top-8 duration-1000\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight drop-shadow-2xl\",\n                                                children: \"Premium Leather Collection\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg md:text-xl lg:text-2xl text-gray-200 max-w-3xl mx-auto leading-relaxed mb-8 drop-shadow-lg\",\n                                                children: \"Discover handcrafted leather jackets made from the finest materials. Each piece combines timeless design with modern craftsmanship.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-8 text-sm md:text-base text-gray-300 mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center drop-shadow-md\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                lineNumber: 82,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Premium Quality\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center drop-shadow-md\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                lineNumber: 86,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Handcrafted\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center drop-shadow-md\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2 fill-yellow-400 text-yellow-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                lineNumber: 90,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"4.9 Rating\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/products\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        size: \"lg\",\n                                                        className: \"bg-white text-gray-900 hover:bg-gray-100 px-8 py-3 text-lg font-semibold shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300\",\n                                                        children: \"Shop Collection\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-6 py-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16\",\n                                children: [\n                                    newDeals.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group relative bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 rounded-2xl overflow-hidden card-hover border border-orange-200/50 shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 md:p-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col md:flex-row md:items-center md:justify-between h-full min-h-[280px]\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 mb-6 md:mb-0 md:pr-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-orange-500 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 116,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                        className: \"text-2xl md:text-3xl font-bold text-gray-900\",\n                                                                        children: \"New Deals\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 117,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                lineNumber: 115,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-3xl md:text-4xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent\",\n                                                                        children: [\n                                                                            \"$\",\n                                                                            newDeals[currentDeal].price\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 121,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-700 text-lg font-medium\",\n                                                                        children: newDeals[currentDeal].name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 124,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                lineNumber: 120,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-4 w-4 fill-yellow-400 text-yellow-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 128,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-sm font-medium text-gray-700\",\n                                                                        children: newDeals[currentDeal].rating\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 129,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-sm text-gray-500\",\n                                                                        children: \"(127 reviews)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 130,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                lineNumber: 127,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        className: \"bg-white/80 hover:bg-white border-orange-200\",\n                                                                        onClick: ()=>setCurrentDeal(currentDeal === 0 ? newDeals.length - 1 : currentDeal + 1),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 140,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 134,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: \"Switch deals\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 142,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        className: \"bg-white/80 hover:bg-white border-orange-200\",\n                                                                        onClick: ()=>setCurrentDeal(currentDeal === newDeals.length - 1 ? 0 : currentDeal + 1),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 149,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 143,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                lineNumber: 133,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0 w-full md:w-48 h-48 md:h-56 relative\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            src: newDeals[currentDeal].image || \"/placeholder.svg\",\n                                                            alt: newDeals[currentDeal].name,\n                                                            fill: true,\n                                                            className: \"object-cover rounded-xl shadow-lg group-hover:scale-105 transition-transform duration-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group relative bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100 rounded-2xl overflow-hidden card-hover border border-gray-200/50 shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 md:p-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col md:flex-row md:items-center md:justify-between h-full min-h-[280px]\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 mb-6 md:mb-0 md:pr-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-2xl md:text-3xl font-bold text-gray-900 mb-4\",\n                                                                children: \"Premium Collection\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                lineNumber: 172,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 mb-6 text-base md:text-lg leading-relaxed\",\n                                                                children: \"Exceptional quality leather jackets crafted by master artisans\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronLeft_ChevronRight_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-4 w-4 fill-yellow-400 text-yellow-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 178,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-sm font-medium text-gray-700\",\n                                                                        children: \"4.9\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 179,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-sm text-gray-500\",\n                                                                        children: \"(500+ reviews)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 180,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                lineNumber: 177,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                href: \"/products\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    className: \"bg-gray-900 hover:bg-gray-800 text-white\",\n                                                                    children: \"View Collection\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 184,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0 w-full md:w-48 h-48 md:h-56 relative\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            src: \"https://images.unsplash.com/photo-1520975954732-35dd22299614?w=300&h=300&fit=crop&crop=center\",\n                                                            alt: \"Premium Leather Jacket\",\n                                                            fill: true,\n                                                            className: \"object-cover rounded-xl shadow-lg group-hover:scale-105 transition-transform duration-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group relative bg-white rounded-2xl p-8 border border-gray-100 card-hover shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-900 text-white px-3 py-1 rounded-full text-xs font-medium inline-block mb-4\",\n                                                            children: \"EXCLUSIVE\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-gray-900 mb-2\",\n                                                            children: \"Signature Series\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-4\",\n                                                            children: \"Limited edition handcrafted pieces\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            size: \"sm\",\n                                                            className: \"bg-gray-900 hover:bg-gray-800\",\n                                                            children: \"Explore\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-24 relative ml-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: \"https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=150&h=150&fit=crop&crop=center\",\n                                                        alt: \"Signature Series\",\n                                                        fill: true,\n                                                        className: \"object-cover rounded-lg shadow-md\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group relative bg-white rounded-2xl p-8 border border-gray-100 card-hover shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-gray-900 mb-2\",\n                                                            children: \"Heritage Collection\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-4\",\n                                                            children: \"Classic designs with modern comfort\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            children: \"View All\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-24 relative ml-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: \"https://images.unsplash.com/photo-1551028719-00167b16eac5?w=150&h=150&fit=crop&crop=center\",\n                                                        alt: \"Heritage Collection\",\n                                                        fill: true,\n                                                        className: \"object-cover rounded-lg shadow-md\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12 animate-in slide-in-from-bottom-4 duration-1000\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                                children: \"Featured Products\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                                                children: \"Carefully selected pieces from our premium collection\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-7xl mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6\",\n                                            children: featuredProducts.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-in slide-in-from-bottom-4\",\n                                                    style: {\n                                                        animationDelay: \"\".concat(index * 100, \"ms\"),\n                                                        animationDuration: \"600ms\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_product_card__WEBPACK_IMPORTED_MODULE_5__.ProductCard, {\n                                                        product: product\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, product.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mt-12\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/products\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                size: \"lg\",\n                                                variant: \"outline\",\n                                                className: \"bg-white hover:bg-gray-50\",\n                                                children: \"View All Products\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\jsm-apps\\\\next\\\\ecom\\\\v0-download-attempt\\\\app\\\\page.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"12V8o694pBYMS4aZ8T6rNPh/ZWE=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});