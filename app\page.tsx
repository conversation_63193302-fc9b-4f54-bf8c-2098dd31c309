"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { Star, Heart, ChevronLeft, ChevronRight, TrendingUp, Award } from 'lucide-react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ProductCard } from "@/components/product/product-card"
import { getProducts } from "@/app/admin/products/actions" // Import from admin actions
import { Product } from "@/lib/types"
import { LoadingSpinner } from "@/components/ui/loading-spinner"

export default function HomePage() {
  const [currentDeal, setCurrentDeal] = useState(0)
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchFeaturedProducts = async () => {
      setLoading(true)
      setError(null)
      const { products: fetchedProducts, error: fetchError } = await getProducts()
      if (fetchError) {
        setError(fetchError)
        // toast.error(`Failed to load featured products: ${fetchError}`); // Consider adding toast
      } else if (fetchedProducts) {
        setFeaturedProducts(fetchedProducts)
      }
      setLoading(false)
    }
    fetchFeaturedProducts()
  }, [])

  const newDeals = featuredProducts.slice(0, 2) // Use fetched products for new deals

  if (loading) {
    return <LoadingSpinner />
  }

  if (error) {
    return <div className="text-red-500 text-center py-16">Error loading homepage products: {error}</div>
  }

  return (
    <div className="min-h-screen bg-white pt-20 md:pt-24">
      <div className="block">
        {/* Hero Section with Background Image */}
        <div className="relative w-full px-6 py-16 flex items-center justify-center min-h-[80vh] perspective-1000">
          {/* 3D Container with curved corners */}
          <div className="relative w-full max-w-7xl mx-auto rounded-3xl overflow-hidden hero-3d hero-shadow">
            {/* Background Image */}
            <div className="absolute inset-0 z-0">
              <Image
                src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1920&h=1080&fit=crop&crop=center"
                alt="Luxury Fashion Store"
                fill
                className="object-cover rounded-3xl"
                priority
              />
              {/* Enhanced gradient overlay for 3D depth */}
              <div className="absolute inset-0 bg-gradient-to-br from-black/30 via-black/40 to-black/60 rounded-3xl" />
              {/* Additional subtle inner shadow for depth */}
              <div className="absolute inset-0 rounded-3xl shadow-inner shadow-black/20" />
              {/* Subtle border glow */}
              <div className="absolute inset-0 rounded-3xl border border-white/10" />
            </div>

            {/* Content */}
            <div className="relative z-10 px-8 py-20 md:py-32 text-center text-white">
              <div className="max-w-4xl mx-auto">
                {/* Hero Header */}
                <div className="animate-in slide-in-from-top-8 duration-1000">
                  <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight drop-shadow-2xl">
                    Premium Leather Collection
                  </h1>
                  <p className="text-lg md:text-xl lg:text-2xl text-gray-200 max-w-3xl mx-auto leading-relaxed mb-8 drop-shadow-lg">
                    Discover handcrafted leather jackets made from the finest materials.
                    Each piece combines timeless design with modern craftsmanship.
                  </p>
                  <div className="flex items-center justify-center space-x-8 text-sm md:text-base text-gray-300 mb-8">
                    <div className="flex items-center drop-shadow-md">
                      <Award className="h-4 w-4 mr-2" />
                      Premium Quality
                    </div>
                    <div className="flex items-center drop-shadow-md">
                      <TrendingUp className="h-4 w-4 mr-2" />
                      Handcrafted
                    </div>
                    <div className="flex items-center drop-shadow-md">
                      <Star className="h-4 w-4 mr-2 fill-yellow-400 text-yellow-400" />
                      4.9 Rating
                    </div>
                  </div>
                  <div className="mt-8">
                    <Link href="/products">
                      <Button size="lg" className="bg-white text-gray-900 hover:bg-gray-100 px-8 py-3 text-lg font-semibold shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300">
                        Shop Collection
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="max-w-7xl mx-auto px-6 py-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
            {/* New Deals Card */}
            {newDeals.length > 0 && (
              <div className="group relative bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 rounded-2xl overflow-hidden card-hover border border-orange-200/50 shadow-lg">
                <div className="p-6 md:p-8">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between h-full min-h-[280px]">
                    <div className="flex-1 mb-6 md:mb-0 md:pr-6">
                      <div className="flex items-center mb-4">
                        <TrendingUp className="h-5 w-5 text-orange-500 mr-2" />
                        <h2 className="text-2xl md:text-3xl font-bold text-gray-900">New Deals</h2>
                      </div>
                      
                      <div className="mb-4">
                        <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                          ${newDeals[currentDeal].price}
                        </div>
                        <div className="text-gray-700 text-lg font-medium">{newDeals[currentDeal].name}</div>
                      </div>

                      <div className="flex items-center mb-6">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="ml-2 text-sm font-medium text-gray-700">{newDeals[currentDeal].rating}</span>
                        <span className="ml-2 text-sm text-gray-500">(127 reviews)</span>
                      </div>

                      <div className="flex items-center space-x-3">
                        <Button
                          variant="outline"
                          size="sm"
                          className="bg-white/80 hover:bg-white border-orange-200"
                          onClick={() => setCurrentDeal(currentDeal === 0 ? newDeals.length - 1 : currentDeal + 1)}
                        >
                          <ChevronLeft className="h-4 w-4" />
                        </Button>
                        <span className="text-xs text-gray-600">Switch deals</span>
                        <Button
                          variant="outline"
                          size="sm"
                          className="bg-white/80 hover:bg-white border-orange-200"
                          onClick={() => setCurrentDeal(currentDeal === newDeals.length - 1 ? 0 : currentDeal + 1)}
                        >
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="flex-shrink-0 w-full md:w-48 h-48 md:h-56 relative">
                      <Image
                        src={newDeals[currentDeal].image || "/placeholder.svg"}
                        alt={newDeals[currentDeal].name}
                        fill
                        className="object-cover rounded-xl shadow-lg group-hover:scale-105 transition-transform duration-500"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Premium Collection Card */}
            <div className="group relative bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100 rounded-2xl overflow-hidden card-hover border border-gray-200/50 shadow-lg">
              <div className="p-6 md:p-8">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between h-full min-h-[280px]">
                  <div className="flex-1 mb-6 md:mb-0 md:pr-6">
                    <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">Premium Collection</h2>
                    <p className="text-gray-600 mb-6 text-base md:text-lg leading-relaxed">
                      Exceptional quality leather jackets crafted by master artisans
                    </p>

                    <div className="flex items-center mb-6">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="ml-2 text-sm font-medium text-gray-700">4.9</span>
                      <span className="ml-2 text-sm text-gray-500">(500+ reviews)</span>
                    </div>

                    <Link href="/products">
                      <Button className="bg-gray-900 hover:bg-gray-800 text-white">
                        View Collection
                      </Button>
                    </Link>
                  </div>

                  <div className="flex-shrink-0 w-full md:w-48 h-48 md:h-56 relative">
                    <Image
                      src="https://images.unsplash.com/photo-1520975954732-35dd22299614?w=300&h=300&fit=crop&crop=center"
                      alt="Premium Leather Jacket"
                      fill
                      className="object-cover rounded-xl shadow-lg group-hover:scale-105 transition-transform duration-500"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Featured Collections */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
            <div className="group relative bg-white rounded-2xl p-8 border border-gray-100 card-hover shadow-lg">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="bg-gray-900 text-white px-3 py-1 rounded-full text-xs font-medium inline-block mb-4">
                    EXCLUSIVE
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Signature Series</h3>
                  <p className="text-gray-600 mb-4">Limited edition handcrafted pieces</p>
                  <Button size="sm" className="bg-gray-900 hover:bg-gray-800">
                    Explore
                  </Button>
                </div>
                <div className="w-24 h-24 relative ml-6">
                  <Image
                    src="https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=150&h=150&fit=crop&crop=center"
                    alt="Signature Series"
                    fill
                    className="object-cover rounded-lg shadow-md"
                  />
                </div>
              </div>
            </div>

            <div className="group relative bg-white rounded-2xl p-8 border border-gray-100 card-hover shadow-lg">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Heritage Collection</h3>
                  <p className="text-gray-600 mb-4">Classic designs with modern comfort</p>
                  <Button size="sm" variant="outline">
                    View All
                  </Button>
                </div>
                <div className="w-24 h-24 relative ml-6">
                  <Image
                    src="https://images.unsplash.com/photo-1551028719-00167b16eac5?w=150&h=150&fit=crop&crop=center"
                    alt="Heritage Collection"
                    fill
                    className="object-cover rounded-lg shadow-md"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Products Section - Updated Card Design */}
          <div className="mt-16">
            <div className="text-center mb-12 animate-in slide-in-from-bottom-4 duration-1000">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Featured Products</h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Carefully selected pieces from our premium collection
              </p>
            </div>

            <div className="max-w-7xl mx-auto">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                {featuredProducts.map((product, index) => (
                  <div
                    key={product.id}
                    className="animate-in slide-in-from-bottom-4"
                    style={{ animationDelay: `${index * 100}ms`, animationDuration: "600ms" }}
                  >
                    <ProductCard product={product} />
                  </div>
                ))}
              </div>
            </div>

            <div className="text-center mt-12">
              <Link href="/products">
                <Button size="lg" variant="outline" className="bg-white hover:bg-gray-50">
                  View All Products
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
