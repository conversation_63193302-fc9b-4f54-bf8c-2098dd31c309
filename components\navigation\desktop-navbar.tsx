"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { Home, ShoppingBag, User, Package, Sparkles } from 'lucide-react'
import { Button } from "@/components/ui/button"
import { useAppSelector } from "@/lib/store/hooks"

export function DesktopNavbar() {
  const pathname = usePathname()
  const cartItemCount = useAppSelector((state) => state.cart.itemCount)

  return (
    <nav className="hidden md:block fixed top-6 left-1/2 transform -translate-x-1/2 z-50">
      <div className="glass-effect bg-white/95 backdrop-blur-xl border border-orange-200/50 rounded-full px-8 py-4 shadow-2xl shadow-orange-500/10 min-w-[600px]">
        <div className="flex items-center justify-between">
          {/* Brand Name */}
          <Link href="/" className="flex items-center">
            <h1 className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent hover:from-blue-600 hover:to-purple-600 transition-all duration-300">
              Leather stuff
            </h1>
          </Link>

          {/* Navigation Icons */}
          <div className="flex items-center space-x-6">
          {/* Home Icon */}
          <Link href="/">
            <Button
              variant="ghost"
              size="icon"
              className={`rounded-full transition-all duration-300 transform hover:scale-110 ${
                pathname === "/" 
                  ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg shadow-blue-500/25" 
                  : "hover:bg-gray-50 text-gray-700 hover:shadow-md"
              }`}
            >
              <Home className="h-5 w-5" />
            </Button>
          </Link>

          {/* Products Icon */}
          <Link href="/products">
            <Button
              variant="ghost"
              size="icon"
              className={`rounded-full transition-all duration-300 transform hover:scale-110 ${
                pathname === "/products" 
                  ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg shadow-blue-500/25" 
                  : "hover:bg-gray-50 text-gray-700 hover:shadow-md"
              }`}
            >
              <Package className="h-5 w-5" />
            </Button>
          </Link>

          {/* Cart Button - Prominent */}
          <Link href="/cart">
            <Button className="bg-gradient-to-r from-gray-900 to-gray-800 hover:from-gray-800 hover:to-gray-700 text-white rounded-full px-6 py-2 flex items-center space-x-2 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
              <ShoppingBag className="h-4 w-4" />
              <span className="font-medium">Cart</span>
              {cartItemCount > 0 && (
                <span className="bg-gradient-to-r from-orange-400 to-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold animate-pulse">
                  {cartItemCount}
                </span>
              )}
              {cartItemCount > 0 && <Sparkles className="h-3 w-3 animate-pulse" />}
            </Button>
          </Link>

          {/* Profile Icon */}
          <Link href="/profile">
            <Button
              variant="ghost"
              size="icon"
              className={`rounded-full transition-all duration-300 transform hover:scale-110 ${
                pathname === "/profile" 
                  ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg shadow-blue-500/25" 
                  : "hover:bg-gray-50 text-gray-700 hover:shadow-md"
              }`}
            >
              <User className="h-5 w-5" />
            </Button>
          </Link>
          </div>
        </div>
      </div>
    </nav>
  )
}
